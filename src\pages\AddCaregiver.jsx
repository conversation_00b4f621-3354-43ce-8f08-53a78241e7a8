import React from "react";
// styled components
import { StyledForm, Container } from "@widgets/UserSettings/style";
import Field, { Input } from "@ui/Field";

// components
import DropFiles from "@components/DropFiles";
import Btn from "@ui/Btn";
import LabeledFormInput from "@ui/LabeledFormInput";
import CustomSelect from "@ui/Select";
import DateInput from "@components/MaskedInputs/Date";
import Phone from "@components/MaskedInputs/Phone";
import PhoneNumberInput from "@ui/PhoneNumberInput";

// utils
import PropTypes from "prop-types";
import countryList from "react-select-country-list";
import { City } from "country-state-city";

// hooks
import { useState } from "react";
import useNotistack from "@hooks/useNotistack";
import Page from "@layout/Page";
import { z } from "zod";
import styled from "styled-components";
import { textSizes } from "@styles/vars";
import { useDispatch, useSelector } from "react-redux";
import { useSnackbar } from "notistack";
import { useSearchParams } from "react-router-dom";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { doc, updateDoc } from "firebase/firestore";
import { db } from "config/firebase.config";
import { COLLECTIONS } from "@constants/app";
import { updateUserAction } from "@store/slices/users";
import { useEffect } from "react";

const caregiverSchema = z.object({
  name: z.string().nonempty("Name is required").min(3, "Must have 3 letters atleast"),
  email: z.string().email("Must be a valid email").nonempty("Email is required"),
  gender: z.string().nonempty("Gender is required"),
  photo: z.string().nonempty("Photo is required"),
  ID_front: z.string().nonempty("ID Front-side is required"),
  ID_back: z.string().nonempty("ID Back-side is required"),
  license: z.string().nonempty("License is required"),
});

export const Label = styled.label`
  font-size: ${textSizes["14"]};
  width: fit-content;
  margin-bottom: 8px;
  display: block;
`;

const StyledField = styled(Field)`
  width: 100%;
`;

const GENDER_SELECT = [
  { label: "Male", value: "MALE" },
  { label: "Female", value: "FEMALE" },
];

const AddCaregiver = () => {
  const { notify } = useNotistack("Your changes have been successfully saved.", "success");
  const hint = "Drag image here or click to select file";

  const dispatch = useDispatch();
  const { enqueueSnackbar } = useSnackbar();
  const { caregivers } = useSelector((state) => state.users);

  const [searchParams] = useSearchParams();
  const caregiver_id = searchParams.get("caregiver_id");

  const {
    handleSubmit,
    control,
    formState: { errors, isSubmitting },
    setValue,
    setError,
    watch,
  } = useForm({
    defaultValues: {
      name: "",
      email: "",
      gender: "",
      ID_back: "",
      ID_front: "",
      license: "",
      nurse: "",
      photo: "",
    },
    resolver: zodResolver(caregiverSchema),
    mode: "all",
  });

  function onSelectImage(key) {
    setValue(key, "file-selected");
    setError(key, { message: undefined });
  }

  async function submitForm(formValues) {
    console.log("form.value >>", formValues);
    const { name, gender } = formValues;

    // UDPATE NURSE
    if (caregiver_id) {
      await updateDoc(doc(db, COLLECTIONS.USERS, caregiver_id), { name, gender })
        .then(() => {
          enqueueSnackbar("Caregiver data updated successfully", { variant: "success" });
          dispatch(updateUserAction({ id: caregiver_id, role: "CAREGIVER", name, gender }));
        })
        .catch((error) => {
          enqueueSnackbar("Error while updating caregiver data", { variant: "error" });
          console.log("UPDAT NURSE >>", error);
        });
    }
  }

  const [selectedCountry, setSelectedCountry] = useState();
  const [selectedCity, setSelectedCity] = useState();
  const [cities, setCities] = useState([]);

  const getCountriesOptions = () => {
    let countries = countryList().getData();
    for (let i = 0; i < countries.length; i++) {
      if (countries[i].value === "RU") {
        countries[i].label = "Russia [terrorist state]";
      }
    }
    return countries;
  };

  const handleCountryChange = (country) => {
    setSelectedCountry(country);
    setSelectedCity(null);
    let options = [];
    const rawData = City.getCitiesOfCountry(country.value);
    rawData.map((item) => options.push({ value: item.name, label: item.name }));
    setCities(options);
  };

  useEffect(() => {
    if (caregiver_id) {
      const current_caregiver = caregivers?.find((item) => item.id === caregiver_id);
      if (current_caregiver) {
        setValue("name", current_caregiver?.name);
        setValue("email", current_caregiver?.email);
        setValue("gender", current_caregiver?.gender);
      }
    }
  }, [caregivers?.length]);

  return (
    <>
      <Page title="Add Caregiver">
        <StyledForm action="#" method="post" id={`add-caregiver`} onSubmit={(e) => e.preventDefault()}>
          <div className="wrapper">
            <DropFiles multiple={false} type="image">
              <i className="icon icon-image" aria-label={hint} />
              <span className="hint">{hint}</span>
            </DropFiles>
            <Container>
              <LabeledFormInput id={`ProfileFirstName`} title="First Name" placeholder="First Name" />
              <LabeledFormInput id={`ProfileLastName`} title="Last Name" placeholder="Last Name" />
              <LabeledFormInput
                id={`ProfileProfileResidence`}
                title="Residence"
                placeholder="Residence"
                customInput={
                  <CustomSelect
                    label={`ProfileProfileResidence`}
                    placeholder="Residence"
                    options={getCountriesOptions()}
                    value={selectedCountry}
                    variant="basic"
                    changeHandler={(e) => handleCountryChange(e)}
                  />
                }
              />
              <LabeledFormInput
                id={`ProfileCity`}
                title="City"
                placeholder="City"
                customInput={
                  <CustomSelect
                    label={`ProfileCity`}
                    placeholder="City"
                    options={cities}
                    value={selectedCity}
                    variant="basic"
                    changeHandler={(e) => setSelectedCity(e)}
                  />
                }
              />
              <LabeledFormInput id={`ProfileStreet`} title="Street" placeholder="Street" />
              <LabeledFormInput id={`ProfileAddress1`} title="Address Line 1" placeholder="Address Line 1" />
              <LabeledFormInput id={`ProfileAddress2`} title="Address Line 2" placeholder="Address Line 2" />
              <LabeledFormInput
                id={`ProfileBirthday`}
                title="Birthday"
                placeholder="Birthday"
                customInput={<Input as={DateInput} id={`ProfileBirthday`} />}
              />
              <LabeledFormInput
                id={`IdFront`}
                title="ID Front Side"
                placeholder="ID Front Side"
                type="file"
                accept=".png"
              />
              <LabeledFormInput
                id={`IdBack`}
                title="ID Back Side"
                placeholder="ID Back Side"
                type="file"
                accept=".png"
              />
              <LabeledFormInput
                id={`Certificate`}
                title="Certificate"
                placeholder="Certificate"
                type="file"
                accept=".pdf"
              />
            </Container>
          </div>
          <Btn text="Save" handler={notify} type="submit" />
        </StyledForm>
      </Page>
    </>
  );
};

export default AddCaregiver;
