import { db } from "config/firebase.config";
import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { collection, doc, getDocs, orderBy, query, setDoc, where, onSnapshot } from "firebase/firestore";
import { COLLECTIONS } from "@constants/app";

const initialState = {
  error: null,
  isLoading: false,
  notifications: [],
  unreadCount:0,
  unsubscribes: [], // Ensure this is always an array
};

// THUNK TO GET ALL TASKS
export const getNotificationsOfAdmin = createAsyncThunk(
  "notifications/getNotificationsOfAdmin",
  async (_, { rejectWithValue, fulfillWithValue }) => {
    try {
      const q = query(
        collection(db, COLLECTIONS.NOTIFICATIONS),
        where("type", "==", "SENT_BY_ADMIN"),
        orderBy("createdAt", "desc"),
      );
      const snapshot = await getDocs(q);
      const arr = snapshot.docs.map((item) => ({ id: item.id, ...item.data() }));
      return fulfillWithValue(arr);
    } catch (error) {
      console.log("GET NOTIFICATIONS OF ADMIN THUNK >> ", error);
      return rejectWithValue(error);
    }
  },
);

// THUNK TO CREATE NEW TASK DOC
export const getNotificationsOfNurse = createAsyncThunk(
  "notifications/getNotificationsOfNurse",
  async (_, { rejectWithValue, fulfillWithValue }) => {
    try {
      const roles = ["ALL", "NURSE"];

      const queries = roles.map((role) => {
        const q = query(
          collection(db, COLLECTIONS.NOTIFICATIONS),
          where("role", "==", role),
          orderBy("createdAt", "desc"),
        );
        return getDocs(q);
      });
      const snapshots = await Promise.all(queries);
      const merged = snapshots.flatMap((snapshot) => snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() })));
      // merged.sort((a, b) => b.createdAt.toMillis() - a.createdAt.toMillis());
      return fulfillWithValue(merged);
    } catch (error) {
      console.log("GET NOTIFICATIONS OF NURSE THUNK >> ", error);
      return rejectWithValue(error);
    }
  },
);

// Add a new thunk for real-time notifications
export const subscribeToNotifications = createAsyncThunk(
  "notifications/subscribeToNotifications",
  async ({ userRole, userId }, { dispatch, rejectWithValue }) => {
    try {
      let roles = [];
      
      if (userRole === "ADMIN") {
        roles = ["SENT_BY_ADMIN"];
      } else if (userRole === "NURSE") {
        roles = ["ALL", "NURSE"];
      } else if (userRole === "CAREGIVER") {
        roles = ["ALL", "CAREGIVER"];
      } else if (userRole === "CLIENT") {
        roles = ["ALL", "CLIENT"];
      }

      const queries = roles.map((role) => {
        if (role === "SENT_BY_ADMIN") {
          return query(
            collection(db, COLLECTIONS.NOTIFICATIONS),
            where("type", "==", "SENT_BY_ADMIN"),
            orderBy("createdAt", "desc")
          );
        } else {
          return query(
            collection(db, COLLECTIONS.NOTIFICATIONS),
            where("role", "==", role),
            orderBy("createdAt", "desc")
          );
        }
      });

      // Add query for notifications targeting specific user IDs
      if (userId) {
        const receiverIdsQuery = query(
          collection(db, COLLECTIONS.NOTIFICATIONS),
          where("receiverIds", "array-contains", userId),
          orderBy("createdAt", "desc")
        );
        queries.push(receiverIdsQuery);
      }

      // Set up real-time listeners
      const unsubscribes = queries.map((q) => {
        return onSnapshot(q, (snapshot) => {
          const notifications = snapshot.docs.map((doc) => ({ 
            id: doc.id, 
            ...doc.data() 
          }));
          
          // Dispatch action to update notifications in real-time
          dispatch(updateNotificationsRealtime(notifications));
        });
      });

      // Return unsubscribe functions to clean up listeners later
      return unsubscribes;
    } catch (error) {
      console.log("SUBSCRIBE TO NOTIFICATIONS ERROR >> ", error);
      return rejectWithValue(error);
    }
  }
);

// THUNK TO GET NOTIFICATIONS FOR ANY USER (considers both role and receiverIds)
export const getNotificationsForUser = createAsyncThunk(
  "notifications/getNotificationsForUser",
  async ({ userRole, userId }, { rejectWithValue, fulfillWithValue }) => {
    try {
      const queries = [];
      
      // Role-based queries
      if (userRole === "ADMIN") {
        queries.push(
          getDocs(query(
            collection(db, COLLECTIONS.NOTIFICATIONS),
            where("type", "==", "SENT_BY_ADMIN"),
            orderBy("createdAt", "desc")
          ))
        );
      } else {
        // Query for notifications targeted to "ALL" users
        queries.push(
          getDocs(query(
            collection(db, COLLECTIONS.NOTIFICATIONS),
            where("role", "==", "ALL"),
            orderBy("createdAt", "desc")
          ))
        );
        
        // Query for notifications targeted to specific role
        queries.push(
          getDocs(query(
            collection(db, COLLECTIONS.NOTIFICATIONS),
            where("role", "==", userRole),
            orderBy("createdAt", "desc")
          ))
        );
      }
      
      // Query for notifications where user ID is in receiverIds array
      if (userId) {
        queries.push(
          getDocs(query(
            collection(db, COLLECTIONS.NOTIFICATIONS),
            where("receiverIds", "array-contains", userId),
            orderBy("createdAt", "desc")
          ))
        );
      }
      
      const snapshots = await Promise.all(queries);
      const allNotifications = snapshots.flatMap((snapshot) => 
        snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }))
      );
      
      // Remove duplicates based on notification ID
      const uniqueNotifications = allNotifications.filter((notification, index, self) =>
        index === self.findIndex((n) => n.id === notification.id)
      );
      
      // Sort by creation date
      uniqueNotifications.sort((a, b) => b.createdAt?.toMillis() - a.createdAt?.toMillis());
      
      return fulfillWithValue(uniqueNotifications);
    } catch (error) {
      console.log("GET NOTIFICATIONS FOR USER THUNK >> ", error);
      return rejectWithValue(error);
    }
  },
);

const notificationSlice = createSlice({
  name: "notifications",
  initialState,
  reducers: {
    addNewNotificationAction: (state, { payload }) => {
      state.notifications = [payload, ...state.notifications];
      // Update unread count
      state.unreadCount = state.notifications.filter(notification => !notification.read).length;
      return state;
    },
    updateNotificationsRealtime: (state, { payload }) => {
      // Create a map of all notifications (existing + new) to handle deduplication
      const notificationMap = new Map();
      
      // Add existing notifications to the map
      state.notifications.forEach(notification => {
        notificationMap.set(notification.id, notification);
      });
      
      // Add/update with new notifications from the payload
      payload.forEach(notification => {
        notificationMap.set(notification.id, notification);
      });
      
      // Convert map back to array and sort
      state.notifications = Array.from(notificationMap.values())
        .sort((a, b) => b.createdAt?.toMillis() - a.createdAt?.toMillis());
      
      // Update unread count
      state.unreadCount = state.notifications.filter(notification => !notification.read).length;
      return state;
    },
    markNotificationsAsRead: (state) => {
      state.notifications = state.notifications.map(notification => ({
        ...notification,
        read: true
      }));
      state.unreadCount = 0;
      return state;
    },
    setUnreadCount: (state, { payload }) => {
      state.unreadCount = payload;
      return state;
    },
    resetUnreadCount: (state) => {
      state.unreadCount = 0;
    },
    setUnsubscribes: (state, { payload }) => {
      // Ensure payload is an array
      state.unsubscribes = Array.isArray(payload) ? payload : [];
    },
    cleanupSubscriptions: (state) => {
      // Add safety check for undefined/null unsubscribes
      if (state.unsubscribes && Array.isArray(state.unsubscribes)) {
        state.unsubscribes.forEach(unsubscribe => {
          if (typeof unsubscribe === 'function') {
            try {
              unsubscribe();
            } catch (error) {
              console.warn('Error cleaning up subscription:', error);
            }
          }
        });
      }
      state.unsubscribes = [];
    }
  },
  extraReducers: (builder) => {
    builder
      // GET NOTIFICATIONS FOR ADMIN
      .addCase(getNotificationsOfAdmin.pending, (state) => {
        state.error = null;
        state.isLoading = true;
      })
      .addCase(getNotificationsOfAdmin.fulfilled, (state, { payload }) => {
        state.notifications = payload;
        state.isLoading = false;
        state.unreadCount=payload.filter(notification=> !notification.read).length;

      })
      .addCase(getNotificationsOfAdmin.rejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      })
      // GET NOTIFICATIONS FOR NURSE
      .addCase(getNotificationsOfNurse.pending, (state) => {
        state.error = null;
        state.isLoading = true;
      })
      .addCase(getNotificationsOfNurse.fulfilled, (state, { payload }) => {
        state.notifications = payload;
        state.isLoading = false;
        state.unreadCount=payload.filter(notification=> !notification.read).length;

      })
      .addCase(getNotificationsOfNurse.rejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      })
      // SUBSCRIBE TO NOTIFICATIONS
      .addCase(subscribeToNotifications.pending, (state) => {
        state.error = null;
        state.isLoading = true;
      })
      .addCase(subscribeToNotifications.fulfilled, (state, { payload }) => {
        // Ensure payload is an array before setting
        state.unsubscribes = Array.isArray(payload) ? payload : [];
        state.isLoading = false;
        state.error = null;
      })
      .addCase(subscribeToNotifications.rejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
        state.unsubscribes = []; // Reset to empty array on error
      })
      // GET NOTIFICATIONS FOR USER
      .addCase(getNotificationsForUser.pending, (state) => {
        state.error = null;
        state.isLoading = true;
      })
      .addCase(getNotificationsForUser.fulfilled, (state, { payload }) => {
        state.notifications = payload;
        state.isLoading = false;
        state.unreadCount=payload.filter(notification=> !notification.read).length;

      })
      .addCase(getNotificationsForUser.rejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      });
  },
});

export const { 
  addNewNotificationAction, 
  updateNotificationsRealtime,
  markNotificationsAsRead, 
  setUnreadCount,
  resetUnreadCount,
  setUnsubscribes,
  cleanupSubscriptions
} = notificationSlice.actions;

export default notificationSlice;
