// styled components
import { LinksList, List, MainItem } from "../style";
import { colors } from "@styles/vars";
import { QtyBadge } from "@ui/Badge/style";
import { useEffect, useMemo } from "react";

// components
import Accordion from "react-bootstrap/Accordion";
import { NavLink } from "react-router-dom";

// hooks
import { useSidebarContext } from "@contexts/sidebarContext";

// menu links
import { menu, admin_menu, nurse_menu } from "@constants/menu";
import { useSelector } from "react-redux";

const Content = () => {
  const { toggleSidebar } = useSidebarContext();
  const activeStyle = { color: colors.blue };
  const { user } = useSelector((state) => state.auth);
  const { chats } = useSelector((state) => state.chats);

  // Calculate unread chats with better logic
  const unreadChats = useMemo(() => {
    if (!chats || !user?.id) return [];
    
    return chats.filter((chat) => {
      const lastMsg = chat?.lastMessage;
      return (
        lastMsg &&
        lastMsg.read === false &&
        lastMsg.senderId !== user?.id
      );
    });
  }, [chats, user?.id]);

  const showUnreadBadge = unreadChats?.length > 0;

  useEffect(() => {
    console.log('Chats updated:', chats?.length, 'Unread count:', unreadChats?.length);
  }, [chats, unreadChats]);

  const sidebar_menu = () => {
    switch (user?.role) {
      case "ADMIN":
        return admin_menu;
      case "NURSE":
        return nurse_menu;
      default:
        return [];
    }
  };

  const isChatItem = (item) => {
    return item.name === "Chats" || item.link === "/nurse_chats";
  };

  return (
    <List as={Accordion}>
      {sidebar_menu()?.map((item, index) => {
        if (item.cat) {
          return (
            <Accordion.Item eventKey={item.cat} key={item.cat}>
              <MainItem as={Accordion.Header}>
                <i className={`icon icon-${item.icon}`}></i> {item.cat}
              </MainItem>
              <Accordion.Body>
                <LinksList>
                  {item.links.map((link) => {
                    return (
                      <li key={link.link}>
                        <NavLink
                          to={link.link}
                          onClick={() => toggleSidebar()}
                          style={({ isActive }) => (isActive ? activeStyle : undefined)}
                        >
                          {link.name}
                        </NavLink>
                      </li>
                    );
                  })}
                </LinksList>
              </Accordion.Body>
            </Accordion.Item>
          );
        } else if (item.link) {
          return (
            <MainItem
              as={NavLink}
              to={item.link}
              onClick={() => toggleSidebar()}
              style={({ isActive }) => (isActive ? activeStyle : undefined)}
              key={item.link}
            >
              <div style={{ display: 'flex', alignItems: 'center', gap: '18px', flex: 1 }}>
                <i className={`icon icon-${item.icon}`}></i>
                <span>{item.name}</span>
              </div>
              {isChatItem(item) && showUnreadBadge > 0 && (
                <QtyBadge 
                  style={{
                    minWidth: '10px',
                    height: '10px',
                    borderRadius: '50%',
                    fontSize: '6px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginLeft: '8px',
                    flexShrink: 0
                  }}
                >
                  {/* {unreadCount > 99 ? '99+' : unreadCount} */}
                </QtyBadge>
              )}
            </MainItem>
          );
        } else return null;
      })}
    </List>
  );
};

export default Content;
