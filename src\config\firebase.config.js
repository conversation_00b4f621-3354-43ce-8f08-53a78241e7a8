import { initializeApp } from "firebase/app";
import { getAuth } from "firebase/auth";
import { getStorage, ref } from "firebase/storage";
import { getFirestore } from "firebase/firestore";
import { getMessaging } from "firebase/messaging";

const config = {
  apiKey: "AIzaSyCF3RwKjw3n55nzD2p2aOY1ScHBLYlbRUQ",
  authDomain: "insyt-care.firebaseapp.com",
  projectId: "insyt-care",
  storageBucket: "insyt-care.firebasestorage.app",
  messagingSenderId: "92475714813",
  appId: "1:92475714813:web:b9314de8ae145364ace294",
  measurementId: "G-KFE07JXQ32",
};

export const app = initializeApp(config);
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);
export const storageRef = ref(storage);
export const messaging = getMessaging(app);
