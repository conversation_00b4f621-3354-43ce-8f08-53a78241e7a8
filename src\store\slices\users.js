import { db } from "config/firebase.config";
import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { collection, getDocs, orderBy, query, where } from "firebase/firestore";
import { COLLECTIONS } from "@constants/app";
import { generateNames } from "@utils/helpers";

const initialState = {
  error: null,
  isLoading: false,
  nurses: [],
  caregivers: [],
  clients: [],
};

const NURSE_DEPARTMENT = { id: "nurse", value: "nurse", label: "Registered Nurse" };
const CAREGIVER_DEPARTMENT = { id: "caregiver", value: "caregiver", label: "Caregiver" };

// THUNK TO GET ALL USERS
export const getAllUsers = createAsyncThunk("users/getAllUsers", async (_, { rejectWithValue, fulfillWithValue }) => {
  try {
    const q = query(collection(db, COLLECTIONS.USERS), orderBy("createdAt", "desc"));
    const snapshot = await getDocs(q);
    const arr = snapshot.docs.map((item) => ({ id: item.id, ...item.data() }));
    const payload = {
      nurses: arr
        .filter((user) => user?.role === "NURSE")
        ?.map((item) => ({
          ...item,
          firstName: generateNames(item?.name)?.firstName,
          lastName: generateNames(item?.name)?.lastName,
          department: [NURSE_DEPARTMENT],
        })),
      caregivers: arr
        .filter((user) => user?.role === "CAREGIVER")
        ?.map((item) => ({
          ...item,
          firstName: generateNames(item?.name)?.firstName,
          lastName: generateNames(item?.name)?.lastName,
          department: [CAREGIVER_DEPARTMENT],
        })),
      clients: arr
        .filter((user) => user?.role === "CLIENT")
        ?.map((item) => ({
          ...item,
          firstName: generateNames(item?.name)?.firstName,
          lastName: generateNames(item?.name)?.lastName,
          reg: new Date(item?.createdAt?.toDate()),
          age: new Date().getFullYear() - new Date(item?.dob)?.getFullYear(),
        })),
    };
    return fulfillWithValue(payload);
  } catch (error) {
    return rejectWithValue(error);
  }
});

// THUNK TO GET ALL CAREGIVERS OF NURSE
export const getAllCaregiversOfNurse = createAsyncThunk(
  "users/getAllCaregiversOfNurse",
  async (_, { rejectWithValue, fulfillWithValue }) => {
    try {
      const q = query(
        collection(db, COLLECTIONS.USERS),
        where("role", "==", "CAREGIVER"),
        orderBy("createdAt", "desc"),
      );
      const snapshot = await getDocs(q);
      const arr = snapshot.docs.map((item) => ({ id: item.id, ...item.data() }));
      const payload = {
        caregivers: arr
          .filter((user) => user?.role === "CAREGIVER")
          ?.map((item) => ({
            ...item,
            firstName: generateNames(item?.name)?.firstName,
            lastName: generateNames(item?.name)?.lastName,
            department: [CAREGIVER_DEPARTMENT],
          })),
      };
      return fulfillWithValue(payload);
    } catch (error) {
      console.log("GET CAREGIVER OF NURSE >>", error);
      return rejectWithValue(error);
    }
  },
);

// THUNK TO GET ALL CLIENTS OF NURSE
export const getAllPatientsOfNurse = createAsyncThunk(
  "users/getAllPatientsOfNurse",
  async (nurseId, { rejectWithValue, fulfillWithValue }) => {
    try {
      const q = query(
        collection(db, COLLECTIONS.USERS),
        where("role", "==", "CLIENT"),
        where("assignedNurse", "==", nurseId),
        orderBy("createdAt", "desc"),
      );
      const snapshot = await getDocs(q);
      const arr = snapshot.docs.map((item) => ({ id: item.id, ...item.data() }));
      const payload = {
        clients: arr
          .filter((user) => user?.role === "CLIENT")
          ?.map((item) => ({
            ...item,
            firstName: generateNames(item?.name)?.firstName,
            lastName: generateNames(item?.name)?.lastName,
            reg: new Date(item?.createdAt?.toDate()),
            age: new Date().getFullYear() - new Date(item?.dob)?.getFullYear(),
          })),
      };
      return fulfillWithValue(payload);
    } catch (error) {
      console.log("GET CLIENTS OF NURSE >>", error);
      return rejectWithValue(error);
    }
  },
);

const usersSlice = createSlice({
  name: "users",
  initialState,
  reducers: {
    addNewUserAction: (state, { payload }) => {
      if (payload?.role === "NURSE") {
        state.nurses = [...state.nurses, payload];
      }
      if (payload?.role === "CAREGIVER") {
        state.caregivers = [...state.caregivers, payload];
      }
      if (payload?.role === "CLIENT") {
        state.clients = [...state.clients, payload];
      }
      return state;
    },
    updateUserAction: (state, { payload }) => {
      if (payload?.role === "NURSE") {
        const index = state.nurses.findIndex((item) => item?.id === payload?.id);
        state.nurses[index] = {
          ...state.nurses[index],
          ...payload,
        };
      }
      if (payload?.role === "CAREGIVER") {
        const index = state.caregivers.findIndex((item) => item?.id === payload?.id);
        state.caregivers[index] = {
          ...state.caregivers[index],
          ...payload,
        };
      }
      if (payload?.role === "CLIENT") {
        const index = state.clients.findIndex((item) => item?.id === payload?.id);
        state.clients[index] = {
          ...state.clients[index],
          ...payload,
        };
      }
      return state;
    },
  },
  extraReducers: (builder) => {
    builder
      // GET ALL USERS (preferred for admin)
      .addCase(getAllUsers.pending, (state) => {
        state.error = null;
        state.isLoading = true;
      })
      .addCase(getAllUsers.fulfilled, (state, { payload }) => {
        state.caregivers = payload.caregivers;
        state.clients = payload.clients;
        state.nurses = payload.nurses;
        state.isLoading = false;
      })
      .addCase(getAllUsers.rejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      })
      // GET CAREGIVERS FOR NURSE
      .addCase(getAllCaregiversOfNurse.pending, (state) => {
        state.error = null;
        state.isLoading = true;
      })
      .addCase(getAllCaregiversOfNurse.fulfilled, (state, { payload }) => {
        state.caregivers = payload.caregivers;
        state.isLoading = false;
      })
      .addCase(getAllCaregiversOfNurse.rejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      })
      // GET CLIENTS FOR NURSE
      .addCase(getAllPatientsOfNurse.pending, (state) => {
        state.error = null;
        state.isLoading = true;
      })
      .addCase(getAllPatientsOfNurse.fulfilled, (state, { payload }) => {
        state.clients = payload.clients;
        state.isLoading = false;
      })
      .addCase(getAllPatientsOfNurse.rejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      });
  },
});

export const { updateUserAction, addNewUserAction } = usersSlice.actions;

export default usersSlice;
