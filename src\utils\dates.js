import moment from 'moment';

export const getYearDaysArray = () => {
    const year = moment().year();
    const days = [];
    const totalDays = moment().year(moment().year()).endOf('year').diff(moment().year(moment().year()).startOf('year'), 'days') + 1;
    for (let i = 1; i <= totalDays; i++) {
        let date = moment().year(year).dayOfYear(i);
        days.push({
            date: date,
            long: date.format('dddd, MMMM DD'),
            short: date.format('DD/MM/YYYY'),
        });
    }
    return days;
}

export const getMonthArray = () => {
    const year = moment().year();
    const months = [];
    for (let i = 1; i <= 12; i++) {
        let month = moment().year(year).month(i - 1);
        months.push({
            month: month,
            formatted: month.format('MMMM, YYYY')
        });
    }
    return months;
};

export const getWeekArray = () => {
    const year = moment().year();
    const weeks = [];
    const totalWeeks = moment().year(moment().year()).endOf('year').diff(moment().year(moment().year()).startOf('year'), 'weeks') + 1;
    for (let i = 1; i <= totalWeeks; i++) {
        let week = moment().year(year).week(i);

        weeks.push({
            week: i,
            startShort: week.startOf('week').format('MMM, DD'),
            endShort: week.endOf('week').format('MMM, DD'),
            startLong: week.startOf('week').format('MMMM, DD'),
            endLong: week.endOf('week').format('MMMM, DD'),
        });
    }
    return weeks;
}

/**
 * Checks if an appointment can be marked as completed based on its date
 * Only appointments scheduled for today or in the past can be completed
 * @param {string} appointmentDateTime - Appointment date in "YYYY-MM-DD HH:mm" format
 * @returns {boolean} True if appointment can be completed, false otherwise
 */
export const canCompleteAppointment = (appointmentDateTime) => {
    if (!appointmentDateTime) return false;

    const appointmentDate = moment(appointmentDateTime, "YYYY-MM-DD HH:mm");
    const today = moment();

    // Allow completion only for today's appointments or past appointments
    return appointmentDate.isSameOrBefore(today, 'day');
};

/**
 * Gets a descriptive tooltip text for appointment completion based on the appointment date
 * @param {string} appointmentDateTime - Appointment date in "YYYY-MM-DD HH:mm" format
 * @returns {string} Tooltip text explaining completion availability
 */
export const getAppointmentCompletionTooltip = (appointmentDateTime) => {
    if (!appointmentDateTime) return "Invalid appointment date";

    const appointmentDate = moment(appointmentDateTime, "YYYY-MM-DD HH:mm");
    const today = moment();

    // if (appointmentDate.isAfter(today, 'day')) {
    //     return `This appointment is scheduled for ${appointmentDate.format("MMMM DD, YYYY")}. Only today's appointments can be marked as completed.`;
    // }

    return "Mark this appointment as completed";
};

/**
 * Formats Firebase Timestamp or regular date objects into a readable format
 * @param {Object|Date|string} timestamp - Firebase Timestamp, Date object, or date string
 * @param {string} format - Moment.js format string (default: "MMM DD, YYYY hh:mm A")
 * @returns {string} Formatted date string
 */
export const formatFirebaseTimestamp = (timestamp, format = "MMM DD, YYYY hh:mm A") => {
    if (!timestamp) return "N/A";

    // Handle Firebase Timestamp
    if (timestamp.toDate && typeof timestamp.toDate === 'function') {
        return moment(timestamp.toDate()).format(format);
    }

    // Handle regular Date or moment object
    return moment(timestamp).format(format);
};