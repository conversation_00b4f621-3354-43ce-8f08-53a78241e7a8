// styled components
import { RangePickerWrapper } from "@widgets/UpcomingAppointments/style";

// components
import Widget from "@components/Widget";
import WidgetHeader from "@components/Widget/WidgetHeader";
import WidgetBody from "@components/Widget/WidgetBody";
import AppointmentItem from "@components/AppointmentItem";
import { AdapterMoment } from "@mui/x-date-pickers/AdapterMoment";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import NoDataPlaceholder from "@components/NoDataPlaceholder";
import ScrollContainer from "@components/ScrollContainer";

// utils
import moment from "moment";

// hooks
import { useRef, useEffect } from "react";
import useContentHeight from "@hooks/useContentHeight";
import { useDispatch, useSelector } from "react-redux";



const UpcomingAppointments = () => {
  const dispatch = useDispatch();
  const headerRef = useRef(null);
  const height = useContentHeight(headerRef);

  // Get data from Redux store
  const { appointments: allAppointments } = useSelector((state) => state.appointments);
  const { clients } = useSelector((state) => state.users);

  // Get upcoming appointments directly from appointments collection
  const upcomingAppointments = allAppointments
    ?.filter(appointment => {
      const appointmentDate = new Date(appointment.startDateTime);
      const now = new Date();
      return appointmentDate >= now && appointment.status === 'SCHEDULED';
    })
    ?.sort((a, b) => new Date(a.startDateTime) - new Date(b.startDateTime))
    ?.slice(0, 10) // Limit to 10 upcoming appointments
    ?.map(appointment => {
      const client = clients?.find(c => c.id === appointment.client);
      return {
        id: appointment.id,
        patient: {
          name: client ? `${client.firstName} ${client.lastName}` : 'Unknown Patient',
          avatar: {
            jpg: client?.photo?.url || '',
            webp: client?.photo?.url || ''
          }
        },
        doctor: {
          name: 'Care Team',
          avatar: {
            jpg: '',
            webp: ''
          }
        },
        type: appointment.serviceType,
        date: new Date(appointment.startDateTime),
        payment: 0,
        phone: client?.phone || '',
        appointment: appointment
      };
    }) || [];

  const isLoadingUpcoming = false; // No longer loading from visits

  // Sort upcoming appointments by date
  const sortedAppointments = [...upcomingAppointments].sort((a, b) => {
    return moment(a.date).diff(moment(b.date));
  });

  const findPatient = (clientId) => clients?.find((item) => item?.id === clientId);

  return (
    <Widget name="UpcomingAppointments" mobile={500}>
      <WidgetHeader title="Upcoming appointments" style={{ paddingBottom: 19 }} flex="column" elRef={headerRef}>
        {/* <RangePickerWrapper>
          <div className="row" ref={anchorRef}>
            <div className="display" onClick={() => setVisible(true)}>
              <span>{selectedDate.format("MMMM DD, YYYY")}</span>
            </div>
          </div>
          <LocalizationProvider dateAdapter={AdapterMoment}>
            <DatePicker
              open={visible}
              value={selectedDate}
              onChange={(newValue) => {
                setSelectedDate(newValue);
                setVisible(false);
              }}
              PopperProps={{ anchorEl: anchorRef.current }}
              PaperProps={{ className: "date-picker" }}
              onClose={() => setVisible(false)}
              disablePast={true}
              renderInput={({ ref, inputProps, disabled, onChange, value }) => (
                <div className="input" ref={ref}>
                  <input
                    value={value && value.toISOString()}
                    onChange={onChange}
                    disabled={disabled}
                    placeholder="MM/DD/YYYY"
                    {...inputProps}
                    type="hidden"
                  />
                </div>
              )}
              shouldDisableDate={(day) => {
                return appointments(day).length === 0;
              }}
            />
          </LocalizationProvider>
        </RangePickerWrapper> */}
      </WidgetHeader>
      <WidgetBody>
        <ScrollContainer height={height}>
          <div className="track">
            {isLoadingUpcoming ? (
              <NoDataPlaceholder text="Loading upcoming appointments..." />
            ) : sortedAppointments.length > 0 ? (
              sortedAppointments.map((appointment) => (
                <AppointmentItem key={appointment.id} data={appointment} patient={findPatient(appointment?.appointment?.client)} animated={true} />
              ))
            ) : (
              <NoDataPlaceholder />
            )}
          </div>
        </ScrollContainer>
      </WidgetBody>
    </Widget>
  );
};

export default UpcomingAppointments;
