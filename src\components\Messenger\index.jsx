// components
import Widget from "@components/Widget";
import Tab from "react-bootstrap/Tab";
import SelectPlaceholder from "@ui/SelectPlaceholder";
import Main from "@components/Messenger/Main";
import NoDataPlaceholder from "@components/NoDataPlaceholder";

const Messenger = ({ variant, user, activeChat }) => {
  return (
    <Tab.Content as={Widget} name="Messenger" mobile={600}>
      {user === "" ? (
        <NoDataPlaceholder />
      ) : (
        // <SelectPlaceholder text="Select a chart to start messaging" />
        <Main variant={variant} user={user} activeChat={activeChat} />
      )}
    </Tab.Content>
  );
};

export default Messenger;
