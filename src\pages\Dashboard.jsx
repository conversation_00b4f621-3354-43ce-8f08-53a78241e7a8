// components
import Page from "@layout/Page";
import ConfirmedDiagnoses from "@widgets/ConfirmedDiagnoses";
import DailyAppointmentChart from "@widgets/DailyAppointmentChart";
import DailyAppointmentsByDoc from "@widgets/DailyAppointmentsByDoc";
import DiagnosesDonut from "@widgets/DiagnosesDonut";
import PatientAppointmentsHistory from "@widgets/PatientAppointmentsHistory";
import PatientOverallAppointments from "@widgets/PatientOverallAppointments";
import RadarAreaChart from "@widgets/RadarAreaChart";
import RecentPatient from "@widgets/RecentPatients";
import TopStaff from "@widgets/TopStaff";
import UpcomingAppointments from "@widgets/UpcomingAppointments";
import { useSelector } from "react-redux";

const Dashboard = () => {
  const { user } = useSelector((state) => state.auth);

  return (
    <>
      {user?.role === "ADMIN" ? <AdminDashboard /> : null}
      {user?.role === "NURSE" ? <NurseDashboard /> : null}
    </>
  );
};

export default Dashboard;

const AdminDashboard = () => {
  return (
    <>
      <Page title="Dashboard">
        <div key="recent-patients">
          <RecentPatient />
        </div>
        <div key="top-staff">
          <TopStaff />
        </div>
        <div key="doctor-upcoming-appointments">
          <UpcomingAppointments />
        </div>
      </Page>
    </>
  );
};

const NurseDashboard = () => {
  return (
    <>
      <Page title="Dashboard">
        <div key="recent-patients">
          <RecentPatient />
        </div>
        <div key="top-staff">
          <TopStaff />
        </div>
        <div key="doctor-upcoming-appointments">
          <UpcomingAppointments />
        </div>
      </Page>
    </>
  );
};
