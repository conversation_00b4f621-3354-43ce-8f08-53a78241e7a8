// styling
import styled from "styled-components/macro";
import theme from "styled-theming";
import { dark, flex, light } from "@styles/vars";

// components
import Modal from "@mui/material/Modal";

// hooks
import { useEffect } from "react";
import useScrollLock from "@hooks/useScrollLock";

// utils
import PropTypes from "prop-types";

export const ModalContent = styled.div`
  padding: 24px;
  background-color: ${theme("theme", {
    light: light.widgetBg,
    dark: dark.widgetBg,
  })};
  ${flex.col};
  border-radius: 8px;
  gap: 16px;

  @media (min-width: 1280px) {
      width: 800px; /* Increased width on desktop */
      height: 600px; /* Increased height on desktop */
    }
`;

const ModalWindow = ({ isVisible, visibilityHandler, children, disableBackdropClick = false }) => {
  // Use custom hook for reliable scroll lock management
  useScrollLock(isVisible);

  const handleClose = (event, reason) => {
    // Prevent closing on backdrop click if disableBackdropClick is true
    if (disableBackdropClick && reason === 'backdropClick') {
      return;
    }

    // Always prevent event propagation to avoid triggering parent handlers
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    visibilityHandler(false);
  };

  return (
    <Modal
      open={isVisible}
      onClose={handleClose}
      sx={{
        zIndex: {xs: 1000, sm: 1000, md: 1000 ,lg: 1000},
        padding: "24px",
        maxHeight: "100vh",
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        overflowY: "auto",
         "& .MuiModal-root": { zIndex: "20001 !important" },
      }}
      

      
      componentsProps={{
        backdrop: {
          transitionDuration: 500,
          sx: {
            backgroundColor: "rgba(0, 0, 0, 0.7)",
          },
        },
      }}
      closeAfterTransition
    >
      <>{children}</>
    </Modal>
  );
};

ModalWindow.propTypes = {
  isVisible: PropTypes.bool.isRequired,
  visibilityHandler: PropTypes.func.isRequired,
  disableBackdropClick: PropTypes.bool,
};

export default ModalWindow;
