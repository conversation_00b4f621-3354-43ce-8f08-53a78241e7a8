import { Box, Typography } from "@mui/material";
import { dark, light } from "@styles/vars";
import moment from "moment";
import styled from "styled-components";
import theme from "styled-theming";

const iconBg = theme("theme", {
  light: light.widgetBg,
  dark: dark.widgetBg,
});

const bg = theme("theme", {
  light: light.bodyBg,
  dark: dark.highlight,
});

const Wrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  background-color: ${bg};
  padding: 0.75rem;
  border-radius: 6px;
  width: 100%;
`;

const TopRow = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  flex-wrap: wrap;

  .left {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex: 1;
    min-width: 0;

    .icon {
      display: grid;
      place-items: center;
      height: 40px;
      width: 40px;
      background-color: ${iconBg};
      border-radius: 99px;
      flex-shrink: 0;
    }

    .title {
      font-weight: 500;
      word-break: break-word;
      overflow-wrap: anywhere;
      line-height: 1.3;
      flex: 1;
    }
  }

  .timestamp {
    display: none;

    @media (min-width: 900px) {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      min-width: fit-content;
    }
  }
`;

const NotificationItem = ({ notification }) => {
  const date = moment(notification?.createdAt?.toDate()).format("DD/MM/YY");
  const time = moment(notification?.createdAt?.toDate()).format("hh:mm A");

  return (
    <Wrapper>
      {/* Top Row with Icon + Title + Timestamp (on md and up) */}
      <TopRow>
        <div className="left">
          <span className="icon">
            <i className="icon icon-bell"></i>
          </span>
          <Typography className="title">{notification?.title}</Typography>
        </div>
        <div className="timestamp">
          <Typography variant="caption">{time}</Typography>
          <Typography variant="caption">{date}</Typography>
        </div>
      </TopRow>

      {/* Body Text */}
      <Typography
        variant="caption"
        sx={{
          wordBreak: "break-word",
          overflowWrap: "anywhere",
          lineHeight: 1.4,
        }}
        style={{margin:"-2px",paddingLeft:"20px"}}
      >
        {notification?.body}
      </Typography>

      {/* Timestamp for mobile (below body) */}
      <Box
        sx={{
          display: {
            xs: "flex",
            md: "none",
          },
          flexDirection: "column",
          alignItems: "flex-end",
        }}
      >
        <Typography variant="caption">{time}</Typography>
        <Typography variant="caption">{date}</Typography>
      </Box>
    </Wrapper>
  );
};

export default NotificationItem;
