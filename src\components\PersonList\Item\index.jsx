// styled components
import { <PERSON>rap<PERSON>, Block } from "./style";

// components
import Avatar from "@ui/Avatar";
import ShapeButton from "@ui/ShapeButton";
import ActionButton from "@ui/ActionButton";
import Reminder from "@ui/Reminder";
import Progress from "@ui/Progress";
import CustomRating from "@ui/CustomRating";
import { motion, AnimatePresence } from "framer-motion";
import { useNavigate } from "react-router-dom";

// utils
import { fadePresence } from "@constants/framer";
import PropTypes from "prop-types";
import { getNameInitials } from "@utils/helpers";
import { useDispatch, useSelector } from "react-redux";
import { useSnackbar } from "notistack";
import { addDoc, collection, doc, Timestamp, updateDoc } from "firebase/firestore";
import { db } from "@config/firebase.config";
import { COLLECTIONS } from "@constants/app";
import EditBtn from "@ui/ActionButton/EditBtn";
import ChatBtn from "@ui/ActionButton/ChatBtn";
import Btn from "@ui/Btn";
import TextBtn from "@ui/ActionButton/TextBtn";
import { CheckCircle, Visibility, Block as BlockIcon, Edit, Chat } from "@mui/icons-material";
import { useState } from "react";
import ConfirmationModal from "@components/ConfirmationModal";
import { updateUserAction } from "@store/slices/users";
import CircularProgressWithLabel from "@ui/CircularProgress";
import NoDataPlaceholder from "@components/NoDataPlaceholder";

const Item = ({ type, data }) => {
  const navigate = useNavigate();

  const { enqueueSnackbar } = useSnackbar();
  const { user: logged_in_user } = useSelector((state) => state.auth);
  const { chats } = useSelector((state) => state.chats);
  const dispatch = useDispatch();

  const [isToggleVisible, setToggleVisible] = useState(false);
  const [isUpdating, setUpdating] = useState(false);

  // Early return for empty state - check before any destructuring
  if (!data || !data?.id) {
    return <NoDataPlaceholder variant="search" />;
  }

  // Safe destructuring after we know data exists
  const { firstName, lastName, online } = data;

  function onEditStaff() {
    navigate(`/add_staff?user_id=${data?.id}`);
  }
  function onEditPatient() {
    navigate(`/add_client?client_id=${data?.id}`);
  }

  async function onClickChat() {
    if (logged_in_user?.role === "NURSE") {
      const chat_found = chats.find(
        (chat) => chat.participants.includes(logged_in_user.id) && chat.participants.includes(data.id),
      );
      if (chat_found) {
        navigate(`/nurse_chats?chat_id=${chat_found?.id}`);
      } else {
        enqueueSnackbar("Initiating chat..", { variant: "info" });
        const payload = {
          createdAt: Timestamp.fromDate(new Date()),
          initiatedBy: logged_in_user?.id,
          lastMessage: {},
          unreadCounts: {
            [data?.id]: 0,
            [logged_in_user?.id]: 0,
          },
          participants: [logged_in_user?.id, data?.id],
          updatedAt: Timestamp.fromDate(new Date()),
        };
        await addDoc(collection(db, COLLECTIONS.CHATS), payload)
          .then((res) => {
            navigate(`/nurse_chats?chat_id=${res?.id}`);
          })
          .catch(() => enqueueSnackbar("Couldn't intiate chat", { variant: "error" }));
      }
    }
  }

  function onClickPatientDetails() {
    navigate(`/clients/${data?.id}`);
  }

  function onClickStaffDetails() {
    navigate(`/staff/${data?.id}`);
  }

  async function onConfirmToggle() {
    setUpdating(true);
    const payload = {
      isActive: !data?.isActive,
    };
    await updateDoc(doc(db, COLLECTIONS.USERS, data?.id), payload)
      .then(() => {
        dispatch(updateUserAction({ id: data?.id, ...payload, role: data?.role }));
        enqueueSnackbar(`Patient ${data?.isActive ? "deactivated" : "activated"} successfully`, { variant: "success" });
      })
      .catch(() => {
        enqueueSnackbar("Couldn't update client status", { variant: "error" });
      });

    setUpdating(false);
    setToggleVisible(false);
  }

  const Common = ({ type }) => {
    return (
      <Block>
        <Avatar
          avatar={{ jpg: data?.photo?.url }}
          alt={`${firstName} ${lastName}`}
          online={online}
          initals={getNameInitials(firstName, lastName)}
        />
        <div className="main">
          <span className="name">
            {firstName} {lastName}
          </span>
          {type === "patient" ? (
            <span className="age">{data?.age} years</span>
          ) : (
            <span className="department">{data?.department?.map((item) => item.label).join(", ") || "No department"}</span>
          )}
        </div>
      </Block>
    );
  };

  const Info = () => {
    return (
      <div className="overview">
        {/* <div className="rating">
          <span style={{ textTransform: "capitalize" }}>{data?.role?.toLowerCase()} Rating</span>
          <CustomRating value={data.rating} />
        </div> */}
        <div className="booked">
          <span>Booked Appointments</span>
          <Progress value={data.booked} />
        </div>
      </div>
    );
  };

  const Layout = () => {
    switch (type) {
      default:
      case "staff":
        return (
          <>
            <Common type={type} />
            {/* <Info /> */}
            <Block className="actions">
              
              {logged_in_user?.role === "NURSE" && (
                <div className="wrapper">
                  <TextBtn handler={onClickChat} label="Chat" icon={<Chat className="icon" />} />
                </div>
              )}
              {/* <div className="wrapper">
                <TextBtn
                  handler={() => setToggleVisible(true)}
                  label={data?.isActive ? "Active" : "Inactive"}
                  icon={data?.isActive ? <CheckCircle className="icon" /> : <BlockIcon className="icon" />}
                  disabled={logged_in_user?.role !== "ADMIN"}
                />
              </div> */}
              {logged_in_user?.role === "ADMIN" ? (
                <div className="wrapper">
                  <TextBtn handler={onEditStaff} label="Edit" icon={<Edit className="icon" />} />
                </div>
              ) : null}

              <div className="wrapper">
                <TextBtn handler={onClickStaffDetails} label="Details" icon={<Visibility className="icon" />} />
              </div>
            </Block>
          </>
        );

      case "patient":
        return (
          <>
            <Common type={type} />
            {data.reminder ? <Reminder reminder={data.reminder} /> : null}
            <Block className="actions">
              <div className="wrapper">
                <CircularProgressWithLabel size={40} value={data?.onboardPercentage || 0} fontSize={"10px"} />
              </div>  
              {/* <div className="wrapper">
                <TextBtn
                  handler={() => setToggleVisible(true)}
                  label={data?.isActive ? "Active" : "Inactive"}
                  icon={data?.isActive ? <CheckCircle className="icon" /> : <BlockIcon className="icon" />}
                  disabled={logged_in_user?.role !== "ADMIN"}
                />
              </div> */}

              {logged_in_user?.role === "NURSE" && (
                <div className="wrapper">
                  <TextBtn handler={onClickChat} label="Chat" icon={<Chat className="icon" />} />
                </div>
              )}

              {logged_in_user?.role === "ADMIN" ? (
                <div className="wrapper">
                  <TextBtn handler={onEditPatient} label="Edit" icon={<Edit className="icon" />} />
                </div>
              ) : null}
              <div className="wrapper">
                <TextBtn handler={onClickPatientDetails} label="Details" icon={<Visibility className="icon" />} />
              </div>
              {/* {logged_in_user?.role === "NURSE" && (
                <ShapeButton icon="comment-text" shape="round" label="Messages" handler={onClickChat} />
              )} */}
            </Block>
          </>
        );
    }
  };

  return (
    <AnimatePresence>
      <Wrapper className={type} as={motion.li} {...fadePresence}>
        <Layout />

        {/* TOGGLE STATUS */}
        <ConfirmationModal
          isOpen={isToggleVisible}
          onCancel={() => setToggleVisible(false)}
          onConfirm={onConfirmToggle}
          title={`${data?.isActive ? "Deactivate" : "Activate"} Patient`}
          confirmText={`${data?.isActive ? "Deactivate" : "Activate"}`}
          cancelText="Cancel"
          onClose={() => setToggleVisible(false)}
          subtitle={`Are you sure you want to ${data?.isActive ? "deactivate" : "activate"} ${firstName}?`}
          isLoading={isUpdating}
        />
      </Wrapper>
    </AnimatePresence>
  );
};

Item.propTypes = {
  type: PropTypes.string.isRequired,
  data: PropTypes.any.isRequired,
};

export default Item;
