import moment from 'moment';

export const patient_tests = [
    {
        id: 'LRmwlkTmRiv',
        type: "blood",
        date: moment().subtract(2, 'hour'),
        doctor: "<PERSON>",
        title: "Cephalin-cholesterol flocculation"
    },
    {
        id: 'EDkfK2u',
        type:  "prenatal",
        date: moment().subtract(2, 'hour'),
        doctor: "<PERSON>",
        title: "Mamm<PERSON>"
    },
    {
        id: '97SbXo',
        type: "blood",
        date: moment().subtract(3, 'hour'),
        doctor: "<PERSON><PERSON>",
        title: "Hepatitis B surface antigen"
    },
    {
        id: 'RYXnQSf',
        type: "blood",
        date: moment(),
        doctor: "<PERSON>",
        title: "Allergy testing"
    },
    {
        id: 'VeOJuSBvq',
        type: "blood",
        date: moment(),
        doctor: "<PERSON>",
        title: "Antibiotic Sensitivity Test"
    },
    {
        id: 'aN6wcKSaUMgY',
        type: "mri",
        date: moment(),
        doctor: "<PERSON><PERSON>",
        title: "MR<PERSON>"
    },
    {
        id: 'ukMuBJSL',
        type:  "prenatal",
        date: moment().subtract(1, 'day'),
        doctor: "<PERSON><PERSON>",
        title: "DNA Testing"
    },
    {
        id: 'wMch17',
        type: "ct",
        date: moment().subtract(1, 'day'),
        doctor: "<PERSON>",
        title: "CT Scan"
    },
    {
        id: 'pkMHKe',
        type: "ultrasound",
        date: moment().subtract(1, 'day'),
        doctor: "Jeffery Nichols",
        title: "Ultrasound diagnostic"
    },
    {
        id: '5EkV9OZ',
        type: "xray",
        date: moment().subtract(1, 'day'),
        doctor: "Katherine Wilson",
        title: "Lungs X-Ray"
    },
    {
        id: 'vYMB5V564wE',
        type: "ecg",
        date: moment().subtract(1, 'day'),
        doctor: "Heleen Carter",
        title: "Cardiovascular ECG"
    },
    {
        id: 'WdUbK4OW4Vn',
        type: "mri",
        date: moment().subtract(1, 'day'),
        doctor: "Jeffery Nichols",
        title: "MRI"
    },
    {
        id: 'kLYqsk',
        type: "mri",
        date: moment().subtract(1, 'month'),
        doctor: "Jeffery Nichols",
        title: "MRI"
    },
    {
        id: 'IftpHTw4p',
        type: "xray",
        date: moment().subtract(1, 'month'),
        doctor: "Katherine Wilson",
        title: "Lungs X-Ray"
    },
    {
        id: 'oCnZPQKY',
        type: "ultrasound",
        date: moment().subtract(1, 'month'),
        doctor: "Jeffery Nichols",
        title: "Ultrasound diagnostic"
    },
    {
        id: 'Y4RjFwyJn3y',
        type: "ct",
        date: moment().subtract(1, 'month'),
        doctor: "Anna Richardson",
        title: "CT Scan"
    },
    {
        id: 'JojvuH7',
        type:  "prenatal",
        date: moment().subtract(1, 'month').subtract(2, 'day'),
        doctor: "Jeffery Nichols",
        title: "DNA Testing"
    },
    {
        id: 'Si1B9W',
        type: "blood",
        date: moment().subtract(1, 'month').subtract(2, 'day'),
        doctor: "Miranda Mccoy",
        title: "Antibiotic Sensitivity Test"
    },
    {
        id: '6zXfLNFdo',
        type: "blood",
        date: moment().subtract(1, 'month').subtract(4, 'day'),
        doctor: "Bella Levine",
        title: "Allergy testing"
    },
    {
        id: 'nPE75vNSE1Ya',
        type: "ecg",
        date: moment().subtract(1, 'month').subtract(4, 'day'),
        doctor: "Heleen Carter",
        title: "Cardiovascular ECG"
    },
    {
        id: 'hEUJrTG',
        type:  "prenatal",
        date: moment().subtract(2, 'month'),
        doctor: "Jeffery Nichols",
        title: "DNA Testing"
    },
    {
        id: 'WCHC85b2on2',
        type: "ct",
        date: moment().subtract(2, 'month'),
        doctor: "Anna Richardson",
        title: "CT Scan"
    },
    {
        id: '0zVMPZO8s',
        type: "blood",
        date: moment().subtract(2, 'month').subtract(3, 'day'),
        doctor: "Herman Ryan",
        title: "Cephalin-cholesterol flocculation"
    },
    {
        id: 'g0ral9eA',
        type:  "prenatal",
        date: moment().subtract(2, 'month').subtract(3, 'day'),
        doctor: "Herbert Reynolds",
        title: "Mammography"
    },
    {
        id: 'SyUtrFzCKe',
        type: "xray",
        date: moment().subtract(2, 'month'),
        doctor: "Katherine Wilson",
        title: "Lungs X-Ray"
    },
    {
        id: 'NjQR8EIcGBR',
        type: "ultrasound",
        date: moment().subtract(2, 'month'),
        doctor: "Jeffery Nichols",
        title: "Ultrasound diagnostic"
    },
];