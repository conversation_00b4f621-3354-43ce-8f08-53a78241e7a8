import { doctors } from "@db/doctors";
import Avatar from "@ui/Avatar";
import { nanoid } from "nanoid";

export const tasksOptions = [
  { value: "work", label: "Work" },
  { value: "travel", label: "Travel" },
  { value: "family", label: "Family" },
  { value: "other", label: "Other" },
];

export const depsOptions = [
  { value: "all", label: "All Departments" },
  // { value: "family", label: "Family Nurse" },
  // { value: "therapy", label: "Therapists" },
  // { value: "dent", label: "Dentists" },
  // { value: "cardio", label: "Cardiologists" },
  { value: "nurse", label: "Registered Nurse" },
  { value: "caregiver", label: "Caregiver" },
];

export const testsOptions = [
  { value: "all", label: "All My Tests" },
  { value: "blood", label: "Blood Count" },
  { value: "xray", label: "X-Ray" },
  { value: "ecg", label: "ECG" },
  { value: "ct", label: "CT Scan" },
  { value: "mri", label: "MRI" },
  { value: "ultrasound", label: "Ultrasound" },
  { value: "prenatal", label: "Prenatal Testing" },
];

export const doctorsOptions = () => {
  let options = [];
  doctors.forEach((doctor) => {
    options.push({
      value: doctor.id,
      label: (
        <div className="user-option">
          <Avatar avatar={doctor.avatar} alt={doctor.name} size={40} />
          <span>{doctor.name}</span>
        </div>
      ),
    });
  });
  return options;
};

// APPOINTMENT DATA STRUCTURE (stored in "Appointments" collection)
// Full schema with all required fields - no separate Visits collection
export const appointmentSchema = {
  id: "document ID", // Firebase document ID (auto-generated)
  nurse: "nurse ID",
  caregiver: "caregiver ID",
  client: "client ID",
  startDateTime: "YYYY-MM-DD HH:mm", // 2025-12-25 17:30
  endDateTime: "YYYY-MM-DD HH:mm", // 2025-12-30 17:30
  comments: "string",
  serviceType: "auxiliary_nurse", // service type for the appointment
  status: "SCHEDULED", // appointment status
  recurrence: {
    frequence: "daily", // daily, weekly, monthly, 60-days
    interval: 1, // number, 1 means consecutive, 2 means alternative, 3 means on every third day, and so on...
    daysOfWeek: ["sat", "sun"], // if frequencey is weekly, then daysOfWeek (array) will be there with selected days
    monthlyDates: ["1", "15", "17"], // if frequencey is monthly, then monthlyDates (array) will be there with selected dates
    endDate: "YYYY-MM-DD", // optional end date for recurrence
  },
  // NEW FIELDS FOR RECURRING APPOINTMENTS
  refID: "unique_series_id", // Common ID for all appointments in the same recurring series (null for single appointments)
  isRecurringInstance: true, // true if this appointment is part of a recurring series, false for single appointments
  originalRecurrence: {}, // Copy of the original recurrence settings for reference
  // VISITS OBJECT - embedded visits data for each appointment
  visits: 
    {
      id: "unique_visit_id", // Firebase document ID for the visit
      date: "YYYY-MM-DD", // visit date
      clockIn: "YYYY-MM-DD HH:mm", // scheduled start time
      clockOut: "YYYY-MM-DD HH:mm", // scheduled end time
      status: "scheduled", // scheduled, completed, cancelled, missed
      notes: "string", // visit notes
      createdAt: "Firestore Timestamp",
      updatedAt: "Firestore Timestamp", // when status was last updated
    }
  ,
  createdAt: "Firestore Timestamp",
  updatedAt: "Firestore Timestamp",
};




