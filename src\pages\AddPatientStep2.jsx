import { StyledForm } from "@widgets/UserSettings/style";
import Field from "@ui/Field";
import CustomSelect from "@ui/Select";
import { Button, Checkbox, CircularProgress, FormControlLabel, Grid, IconButton } from "@mui/material";
import { Add, Close } from "@mui/icons-material";
import { CheckBox, CheckBoxOutlineBlank } from "@mui/icons-material";
import { theme } from "../styles/mui-theme";
import { Controller, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Box, Typography } from "@mui/material";
import styled from "styled-components";
import { textSizes } from "@styles/vars";
import { useDispatch } from "react-redux";
import { useEffect } from "react";
import { COLLECTIONS } from "@constants/app";
import { updateUserAction } from "@store/slices/users";
import { useSnackbar } from "notistack";
import { doc, updateDoc } from "firebase/firestore";
import { db } from "../config/firebase.config";
import TextArea from "@ui/TextArea/TextArea";
import { useSearchParams } from "react-router-dom";

const Label = styled.label`
  font-size: ${textSizes["14"]};
  width: fit-content;
  margin-bottom: 8px;
  display: block;
`;

const StyledField = styled(Field)`
  width: 100%;
`;

const KNOWN_DIAGNOSES = ["Diabetes", "Hypertension", "Stroke", "Dementia", "Cancer"];
const IS_HOSPITALIZED_RECENTLY = [
  { label: "Yes", value: true },
  { label: "No", value: false },
];

const step2Schema = z
  .object({
    reasonToRequest: z.string().nonempty("Explain your reason").min(10, "Must be at least 10 characters long"),
    isHospitalizedRecently: z.boolean().default(false),
    reasonForHospitalization: z.string().optional().default(""),
    knownDiagnoses: z.array(z.string()).default([]),
    otherDiagnosisText: z.string().optional(),
    medicationText: z.string().optional(),
    medications: z.array(z.string()).default([]),
    allergyText: z.string().optional(),
    allergies: z.array(z.string()).default([]),
    restrictionText: z.string().optional().default(""),
    restrictions: z.array(z.string()).default([]),
  })
  .superRefine((data, ctx) => {
    if (data.isHospitalizedRecently && data.reasonForHospitalization.length < 1) {
      ctx.addIssue({
        path: ["reasonForHospitalization"],
        code: z.ZodIssueCode.custom,
        message: "Expalain why did you hospitalized recently",
      });
    }
    if (data?.knownDiagnoses.find((item) => item.startsWith("Other:")) && !data?.otherDiagnosisText?.trim()) {
      ctx.addIssue({
        path: ["otherDiagnosisText"],
        code: z.ZodIssueCode.custom,
        message: "Provide the other diagnosis",
      });
    }
  });

const MAX_DATE_DOB = new Date();
MAX_DATE_DOB.setFullYear(new Date().getFullYear() - 5);

const AddPatienStep2 = ({ gotoNext, goBack, canGoBack, setCurrentPatient, currentPatient }) => {
  const { enqueueSnackbar } = useSnackbar();
  const dispatch = useDispatch();
  const [searchParams, setSearchParams] = useSearchParams();

  const {
    handleSubmit,
    control,
    formState: { errors, isSubmitting },
    setValue,
    watch,
  } = useForm({
    defaultValues: {
      allergyText: "",
      diagnosisText: "",
      medicationText: "",
      allergies: [],
      knownDiagnoses: [],
      otherDiagnosisText: "",
      medications: [],
      restrictions: [],
      restrictionText: "",
      reasonToRequest: "",
      reasonForHospitalization: "",
    },
    resolver: zodResolver(step2Schema),
    mode: "all",
  });

  // * ON ADD ALLERGY
  function onAddAllergy() {
    if (watch("allergyText")?.trim()) {
      const arr = [...watch("allergies"), watch("allergyText")];
      setValue("allergies", arr);
    }
    setValue("allergyText", "");
  }

  // * ON ADD MEDICATION
  function onAddMedication() {
    if (watch("medicationText")?.trim()) {
      const arr = [...watch("medications"), watch("medicationText")];
      setValue("medications", arr);
    }
    setValue("medicationText", "");
  }

  // * ON ADD DIAGNOSIS
  function onAddRestriction() {
    if (watch("restrictionText")?.trim()) {
      const arr = [...watch("restrictions"), watch("restrictionText")];
      setValue("restrictions", arr);
    }
    setValue("restrictionText", "");
  }

  // * ON DELETE ALLERGY
  function onDeleteAllergy(val) {
    const arr = watch("allergies")?.filter((item) => item !== val);
    setValue("allergies", arr);
  }

  // * ON DELETE MEDICATION
  function onDeleteMedication(val) {
    const arr = watch("medications")?.filter((item) => item !== val);
    setValue("medications", arr);
  }

  // * ON ADD/REMOVE KNOWN DIAGNOSIS
  function onToggleKnownDiagnosis(val) {
    const currentDiagnoses = watch("knownDiagnoses");
    if (currentDiagnoses.includes(val)) {
      setValue(
        "knownDiagnoses",
        currentDiagnoses.filter((diagnosis) => diagnosis !== val),
      );
      val === "Other:" && setValue("otherDiagnosisText", "");
    } else {
      setValue("knownDiagnoses", [...currentDiagnoses, val]);
    }
  }

  // * SUBMIT FORM
  async function submitForm(formValues) {
    const {
      allergies,
      isHospitalizedRecently,
      knownDiagnoses,
      medications,
      otherDiagnosisText,
      reasonForHospitalization,
      reasonToRequest,
      restrictions,
    } = formValues;
    const formatted_known_diagnoses = knownDiagnoses.map((diagnosis) =>
      diagnosis === "Other:" ? `Other: ${otherDiagnosisText}` : diagnosis,
    );
    const payload = {
      reasonToRequest,
      isHospitalizedRecently,
      reasonForHospitalization,
      knownDiagnoses: formatted_known_diagnoses,
      allergies,
      medications,
      restrictions,
      currentOnboardStep: 3,
      onboardPercentage: currentPatient?.onboardPercentage > 50 ? currentPatient?.onboardPercentage : 50,
    };

    await updateDoc(doc(db, COLLECTIONS.USERS, currentPatient?.id), payload)
      .then(() => {
        const updated_patient = {
          ...currentPatient,
          ...payload,
        };
        dispatch(updateUserAction(updated_patient));
        enqueueSnackbar("Pateint's data updated", { variant: "success" });
        setCurrentPatient(updated_patient);
        gotoNext();
      })
      .catch(() => enqueueSnackbar("Couldn't update the patient's data", { variant: "error" }));
  }

  useEffect(() => {
    if (currentPatient?.id) {
      currentPatient?.reasonToRequest && setValue("reasonToRequest", currentPatient?.reasonToRequest);
      currentPatient?.isHospitalizedRecently &&
        setValue("isHospitalizedRecently", currentPatient?.isHospitalizedRecently);
      currentPatient?.reasonForHospitalization &&
        setValue("reasonForHospitalization", currentPatient?.reasonForHospitalization);
      currentPatient?.knownDiagnoses?.length &&
        setValue(
          "knownDiagnoses",
          currentPatient?.knownDiagnoses?.map((item) => item?.split(" ")[0]),
        );

      if (currentPatient?.knownDiagnoses?.find((item) => item?.includes("Other:"))) {
        setValue(
          "otherDiagnosisText",
          currentPatient?.knownDiagnoses?.find((item) => item?.includes("Other:"))?.split("Other: ")[1],
        );
      }

      currentPatient?.allergies?.length && setValue("allergies", currentPatient?.allergies);
      currentPatient?.medications?.length && setValue("medications", currentPatient?.medications);
      currentPatient?.restrictions?.length && setValue("restrictions", currentPatient?.restrictions);
    }
  }, [currentPatient]);

  return (
    <>
      <StyledForm onSubmit={handleSubmit(submitForm)}>
        <Grid container spacing={2}>
          <Grid size={12} mt={2}>
            <Typography variant="h6" fontWeight={500}>
              Presenting Complaints & Medical History
            </Typography>
          </Grid>

          {/* REASON TO REQUEST */}
          <Grid size={12}>
            <Controller
              name="reasonToRequest"
              control={control}
              render={({ field }) => (
                <>
                  <Label htmlFor="reasonToRequest">Why is home care being requested?</Label>
                  <TextArea {...field} placeholder="Explain your reason" id="reasonToRequest" />
                </>
              )}
            />
            {errors?.reasonToRequest?.message && (
              <Typography color="error" variant="caption">
                {errors?.reasonToRequest?.message}
              </Typography>
            )}
          </Grid>

          {/* IS HOSPITALIZED RECENTLY */}
          <Grid size={{ xs: 12, sm: 6 }}>
            <Controller
              name="isHospitalizedRecently"
              control={control}
              render={({ field }) => (
                <>
                  <Label htmlFor="isHospitalizedRecently">Any recent hospitalizations?</Label>
                  <CustomSelect
                    placeholder="Select"
                    options={[
                      { label: "Yes", value: true },
                      { label: "No", value: false },
                    ]}
                    changeHandler={(option) => field.onChange(option.value)}
                    value={IS_HOSPITALIZED_RECENTLY.find((item) => item.value === field.value)}
                    id="isHospitalizedRecently"
                  />
                </>
              )}
            />
          </Grid>

          {/* REASON FOR HOSPITALIZATION */}
          <Grid size={{ xs: 12, sm: 6 }}>
            <Controller
              name="reasonForHospitalization"
              control={control}
              render={({ field }) => (
                <>
                  <Label htmlFor="reasonForHospitalization">Why did you hospitalize recently?</Label>
                  <TextArea
                    {...field}
                    placeholder="Explain your reason"
                    id="reasonForHospitalization"
                    rows={1}
                    disabled={!watch("isHospitalizedRecently")}
                  />
                </>
              )}
            />
            {errors?.reasonForHospitalization?.message && (
              <Typography color="error" variant="caption">
                {errors?.reasonForHospitalization?.message}
              </Typography>
            )}
          </Grid>

          <Grid size={12}>
            <Label htmlFor="KnownDiagnoses">Known Diagnoses</Label>
            {KNOWN_DIAGNOSES.map((item) => (
              <FormControlLabel
                sx={{ mb: 1 }}
                label={item}
                control={
                  <Checkbox
                    color="primary"
                    checkedIcon={<CheckBox />}
                    icon={<CheckBoxOutlineBlank fill="#2D2D2D" opacity={0.5} />}
                    checked={watch("knownDiagnoses")?.includes(item)}
                    onChange={() => onToggleKnownDiagnosis(item)}
                  />
                }
              />
            ))}

            {/* OTHER */}
            <FormControlLabel
              label="Other"
              sx={{ mb: 1 }}
              control={
                <Checkbox
                  color="primary"
                  checkedIcon={<CheckBox />}
                  icon={<CheckBoxOutlineBlank fill="#2D2D2D" opacity={0.5} />}
                  checked={watch("knownDiagnoses")?.find((item) => item.startsWith("Other:"))}
                  onChange={() => onToggleKnownDiagnosis("Other:")}
                  key={watch("knownDiagnoses")?.length}
                />
              }
            />
            {watch("knownDiagnoses")?.find((item) => item.startsWith("Other:")) ? (
              <Controller
                name="otherDiagnosisText"
                control={control}
                render={({ field }) => {
                  return (
                    <>
                      <StyledField type="text" id="otherDiagnosisText" {...field} placeholder="Other Diagnosis" />
                      {errors?.otherDiagnosisText?.message && (
                        <Typography color="error" variant="caption">
                          {errors?.otherDiagnosisText?.message}
                        </Typography>
                      )}
                    </>
                  );
                }}
              />
            ) : null}
          </Grid>

          {/* ALLERGIES */}
          <Grid size={12} position="relative" height="fit-content">
            <Controller
              name="allergyText"
              control={control}
              render={({ field }) => {
                return (
                  <>
                    <Label htmlFor="allergies">Allergies</Label>
                    <Box display="flex" columnGap={1}>
                      <StyledField
                        type="text"
                        id="allergies"
                        placeholder="Add allergy"
                        {...field}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            e.preventDefault();
                            onAddAllergy();
                          }
                        }}
                      />
                      <Button onClick={onAddAllergy} type="button" variant="outlined">
                        <Add />
                      </Button>
                    </Box>
                  </>
                );
              }}
            />

            {/* ALLERGIES LIST */}
            {watch("allergies")?.length ? (
              <Box display="flex" flexWrap="wrap" mt={1} gap={1}>
                {watch("allergies")?.map((item) => (
                  <Box
                    sx={{
                      backgroundColor: theme.palette.background.default,
                      color: theme.palette.common.black,
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      columnGap: 2,
                      borderRadius: 4,
                      pl: 1.5,
                      py: 0.5,
                    }}
                  >
                    <Typography>{item}</Typography>
                    <IconButton size="small" onClick={() => onDeleteAllergy(item)}>
                      <Close sx={{ height: 14, width: 14 }} />
                    </IconButton>
                  </Box>
                ))}
              </Box>
            ) : null}
          </Grid>

          {/* MEDICATIONS */}
          <Grid size={12} position="relative" height="fit-content">
            <Controller
              name="medicationText"
              control={control}
              render={({ field }) => {
                return (
                  <>
                    <Label htmlFor="medications">Medications</Label>
                    <Box display="flex" columnGap={1}>
                      <StyledField
                        type="text"
                        id="medications"
                        placeholder="Add medication"
                        {...field}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            e.preventDefault();
                            onAddMedication();
                          }
                        }}
                      />
                      <Button onClick={onAddMedication} type="button" variant="outlined">
                        <Add />
                      </Button>
                    </Box>
                  </>
                );
              }}
            />

            {/* MEDICATIONS LIST */}
            {watch("medications")?.length ? (
              <Box display="flex" flexWrap="wrap" mt={1} gap={1}>
                {watch("medications")?.map((item) => (
                  <Box
                    sx={{
                      backgroundColor: theme.palette.background.default,
                      color: theme.palette.common.black,
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      columnGap: 2,
                      borderRadius: 4,
                      pl: 1.5,
                      py: 0.5,
                    }}
                  >
                    <Typography>{item}</Typography>
                    <IconButton size="small" onClick={() => onDeleteMedication(item)}>
                      <Close sx={{ height: 14, width: 14 }} />
                    </IconButton>
                  </Box>
                ))}
              </Box>
            ) : null}
          </Grid>

          {/* RESCTRICTIONS */}
          <Grid size={12} position="relative" height="fit-content">
            <Label htmlFor="Resctrictions"> Nutritional Concerns or Restrictions</Label>
            <Box display="flex" columnGap={1}>
              <Controller
                control={control}
                name="restrictionText"
                render={({ field }) => (
                  <StyledField
                    type="text"
                    id="Resctrictions"
                    {...field}
                    placeholder="Add restriction"
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        e.preventDefault();
                        onAddRestriction();
                      }
                    }}
                  />
                )}
              />
              <Button onClick={onAddRestriction} type="button" variant="outlined">
                <Add />
              </Button>
            </Box>

            {/* RESTRICTIONS LIST */}
            {watch("restrictions")?.length ? (
              <Box display="flex" flexWrap="wrap" mt={1} gap={1}>
                {watch("restrictions")?.map((item) => (
                  <Box
                    sx={{
                      backgroundColor: theme.palette.background.default,
                      color: theme.palette.common.black,
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      columnGap: 2,
                      borderRadius: 4,
                      pl: 1.5,
                      py: 0.5,
                    }}
                  >
                    <Typography>{item}</Typography>
                    <IconButton size="small" onClick={() => onDeleteMedication(item)}>
                      <Close sx={{ height: 14, width: 14 }} />
                    </IconButton>
                  </Box>
                ))}
              </Box>
            ) : null}
          </Grid>

          <Grid
            size={12}
            sx={{
              display: "flex",
              marginTop: 2,
              justifyContent: "space-between",
              alignItems: "center",
              gap: 1.5,
            }}
          >
            <Button
              variant="outlined"
              color="primary"
              type="button"
              sx={{
                fontSize: 16,
                borderRadius: 2,
                textTransform: "none",
                fontWeight: 400,
                maxWidth: 200,
                width: "100%",
              }}
              disabled={!canGoBack || isSubmitting}
              onClick={goBack}
            >
              {"Back"}
            </Button>
            <Button
              variant="contained"
              color="primary"
              type="submit"
              sx={{
                fontSize: 16,
                borderRadius: 2,
                textTransform: "none",
                fontWeight: 400,
                maxWidth: 200,
                width: "100%",
              }}
              disabled={isSubmitting}
            >
              {"Next"}
              {isSubmitting ? <CircularProgress size={16} color="inherit" sx={{ marginLeft: 1 }} /> : null}
            </Button>
          </Grid>
        </Grid>
      </StyledForm>
    </>
  );
};

export default AddPatienStep2;
