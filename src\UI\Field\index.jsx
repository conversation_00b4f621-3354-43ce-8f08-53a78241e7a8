// styling
import styled from "styled-components/macro";
import { colors, dark, light, textSizes } from "@styles/vars";
import theme from "styled-theming";

// utils
import PropTypes from "prop-types";

export const Input = styled.input`
  height: 40px;
  padding: 10px 16px;
  border-radius: 8px;
  border: 1px solid transparent;
  font-size: ${textSizes["14"]};
  ${theme("theme", {
    light: `
    background-color: ${light.highlight};
   `,
    dark: `
    background-color: ${dark.highlight};
   `,
  })};
  transition: border-color var(--transition), box-shadow var(--transition);

  &.error {
    border-color: ${colors.error};
  }

  &:hover {
    box-shadow: ${theme("theme", {
      light: `0 0 0 2px ${colors.primary}`,
      dark: `0 0 0 2px ${colors.primary}`,
    })};
  }

  &:focus {
    box-shadow: 0 0 0 2px ${colors.primary};
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    box-shadow: none;
    background-color: ${theme("theme", {
      light: `${light.highlight}`,
      dark: `${dark.highlight}`,
    })};
  }

  &[type="file"] {
    cursor: pointer;
    position: relative;
    &::-webkit-file-upload-button {
      visibility: hidden;
    }
    &::before {
      content: "Choose file";
      display: inline-block;
      position: absolute;
      top:0, bottom: 0;
      margin-top: -3px;
      margin-left: -10px;
      padding: 2px 6px;
      margin-right: 4px;
      border-radius: 4px;
      background-color: ${colors.primary};
      color: white;
      font-size: ${textSizes["14"]};
    }
    &:hover::before {
      background-color: ${colors.primary_dark};
    }
    &:disabled::before {
      background-color: ${theme("theme", {
        light: `${light.highlight}`,
        dark: `${dark.highlight}`,
      })};
      color: ${theme("theme", {
        light: `${colors.text}`,
        dark: `${colors.text}`,
      })};
    }
  }
`;

const Field = (props) => {
  return <Input {...props} />;
};

Field.propTypes = {
  type: PropTypes.oneOf(["text", "password", "email", "number", "search", "file"]),
  placeholder: PropTypes.string,
};

export default Field;
