import { useEffect, useRef } from 'react';

/**
 * Custom hook to manage scroll lock state reliably
 * Prevents scroll lock from persisting when components unmount during navigation
 */
const useScrollLock = (isLocked) => {
  const lockCountRef = useRef(0);
  const html = document.documentElement;

  useEffect(() => {
    if (isLocked) {
      lockCountRef.current += 1;
      html.classList.add('no-scroll');
    } else {
      lockCountRef.current = Math.max(0, lockCountRef.current - 1);
      if (lockCountRef.current === 0) {
        html.classList.remove('no-scroll');
      }
    }

    // Cleanup function to ensure scroll lock is removed when component unmounts
    return () => {
      if (isLocked) {
        lockCountRef.current = Math.max(0, lockCountRef.current - 1);
        if (lockCountRef.current === 0) {
          html.classList.remove('no-scroll');
        }
      }
    };
  }, [isLocked, html]);

  // Additional cleanup on unmount
  useEffect(() => {
    return () => {
      html.classList.remove('no-scroll');
    };
  }, [html]);
};

export default useScrollLock;
