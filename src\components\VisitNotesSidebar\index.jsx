import React, { useState } from "react";
import {
  <PERSON>er,
  <PERSON>,
  Typo<PERSON>,
  IconButton,
  Di<PERSON>r,
  <PERSON>,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Skeleton,
} from "@mui/material";
import { Close } from "@mui/icons-material";
import styled from "styled-components";
import theme from "styled-theming";
import { colors, dark, light } from "@styles/vars";
import { formatFirebaseTimestamp } from "@utils/dates";

// Import icons
import MoodIcon from "../../assets/mood.png";
import MobilityIcon from "../../assets/mobility.png";
import AppetiteIcon from "../../assets/appitete.png";
import services from "../../assets/services.png";

const SidebarContainer = styled(Box)`
  width: 320px;
  height: 100%;
  padding: 16px;
  background-color: ${theme("theme", {
    light: "#f1f5f8",
    dark: dark.bodyBg,
  })};

  @media (max-width: 768px) {
    width: 100vw;
    padding: 16px;
  }
`;

const SidebarHeader = styled(Box)`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid
    ${theme("theme", {
      light: "#e5e7eb",
      dark: "#2d313a",
    })};
`;

const SectionContainer = styled(Box)`
  margin-bottom: 24px;
`;

const SectionTitle = styled(Typography)`
  font-weight: 600;
  font-size: 12px;
  margin-bottom: 12px;
  color: ${theme("theme", {
    light: "#1f2937",
    dark: "#f9fafb",
  })};
`;

const InfoCard = styled(Card)`
  margin-bottom: 16px;
  background-color: ${theme("theme", {
    light: light.widgetBg,
    dark: dark.widgetBg,
  })};
  border: 1px solid
    ${theme("theme", {
      light: "#e5e7eb",
      dark: "#2d313a",
    })};
  box-shadow: none;
`;

const MedicationChip = styled(Chip)`
  margin: 4px 4px 4px 0;
  background-color: ${colors.blue};
  color: white;
  font-size: 12px;
`;

const PatientImageContainer = styled(Box)`
  position: relative;
  max-width: 100%;
  max-height: 170px;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s ease;

  &:hover {
    transform: scale(1.02);
  }
`;

const PatientImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
`;

const StatusCard = styled(Box)`
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background-color: ${theme("theme", {
    light: "#f8f9fa",
    dark: "#2d313a",
  })};
  border-radius: 12px;
  border: 1px solid
    ${theme("theme", {
      light: "#e9ecef",
      dark: "#3d4147",
    })};
  margin-bottom: 12px;
`;

const StatusIconContainer = styled(Box)`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;

  &.mood {
    background-color: #fff3cd;
  }

  &.appetite {
    background-color: #f8d7da;
  }

  &.mobility {
    background-color: #d1ecf1;
  }
`;

const StatusIcon = styled.img`
  width: 24px;
  height: 24px;
  object-fit: contain;
`;

const StatusContent = styled(Box)`
  flex: 1;
  min-width: 0;
`;

const StatusTitle = styled(Typography)`
  font-weight: 600;
  font-size: 12px;
  margin-bottom: 4px;
  color: ${theme("theme", {
    light: "#1f2937",
    dark: "#f9fafb",
  })};
`;

const StatusDescription = styled(Typography)`
  font-size: 12px;
  color: ${theme("theme", {
    light: "#6b7280",
    dark: "#9ca3af",
  })};
  line-height: 1.4;
`;

const ServiceCard = styled(Box)`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: ${theme("theme", {
    light: "#f8f9fa",
    dark: "#2d313a",
  })};
  border-radius: 12px;
  border: 1px solid
    ${theme("theme", {
      light: "#e9ecef",
      dark: "#3d4147",
    })};
  margin-bottom: 12px;
`;

const ServiceLeft = styled(Box)`
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
`;

const ServiceIconContainer = styled(Box)`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  background-color: #fff3cd;
`;

const ServiceIcon = styled.img`
  width: 24px;
  height: 24px;
  object-fit: contain;
`;

const ServiceName = styled(Typography)`
  font-weight: 500;
  font-size: 10px;
  color: ${theme("theme", {
    light: "#1f2937",
    dark: "#f9fafb",
  })};
  line-height: 0.7;
  word-break: break-word;
  flex: 1;
  padding-right: 8px;

  @media (max-width: 768px) {
    font-size: 8px;
  }
`;

const ServiceTime = styled(Typography)`
  font-size: 8px;
  color: ${theme("theme", {
    light: "#6b7280",
    dark: "#9ca3af",
  })};
  font-weight: 300;
  white-space: nowrap;
  flex-shrink: 0;

  @media (max-width: 768px) {
    font-size: 6px;
  }
`;

const SignatureContainer = styled(Box)`
  // padding: 16px;
  background-color: ${theme("theme", {
    light: "#f8f9fa",
    dark: "#2d313a",
  })};
  border-radius: 12px;
  border: 1px solid
    ${theme("theme", {
      light: "#e9ecef",
      dark: "#3d4147",
    })};
  margin-bottom: 12px;
  text-align: center;
`;

const SignatureImage = styled.img`
  max-width: 100%;
  max-height: 170px;
  border-radius: 8px;
  border: 1px solid
    ${theme("theme", {
      light: "#e9ecef",
      dark: "#3d4147",
    })};
  cursor: pointer;
  transition: transform 0.2s ease;

  &:hover {
    transform: scale(1.02);
  }
`;

const SignatureTitle = styled(Typography)`
  font-weight: 600;
  font-size: 12px;
  margin-bottom: 12px;
  color: ${theme("theme", {
    light: "#1f2937",
    dark: "#f9fafb",
  })};
`;

const PatientImageWithLoading = ({ image, index }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  const handleImageLoad = () => {
    setIsLoading(false);
  };

  const handleImageError = () => {
    setIsLoading(false);
    setHasError(true);
  };

  const handleImageClick = () => {
    if (!hasError) {
      window.open(image.url, "_blank");
    }
  };

  if (hasError) {
    return null; // Don't render anything if image failed to load
  }

  return (
    <PatientImageContainer onClick={handleImageClick}>
      {isLoading && (
        <Skeleton variant="rectangular" width="100%" height="100%" sx={{ position: "absolute", top: 0, left: 0 }} />
      )}
      <PatientImage
        src={image.url}
        alt={`Patient image ${index + 1}`}
        onLoad={handleImageLoad}
        onError={handleImageError}
        style={{ display: isLoading ? "none" : "block" }}
      />
    </PatientImageContainer>
  );
};

const VisitNotesSidebar = ({ open, onClose, visitNotes, appointment }) => {
  if (!visitNotes) return null;

  const { mood, appetite, mobility, medications = [], patientImages = [], completedServices = [] } = visitNotes;

  const patientSignature = appointment?.patientSignature;

  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: {
          zIndex: 1300,
        },
      }}
    >
      <SidebarContainer>
        <SidebarHeader>
          <Typography variant="h6" fontWeight={600}>
            Visit Notes
          </Typography>
          <IconButton onClick={onClose} size="small">
            <Close />
          </IconButton>
        </SidebarHeader>

        {/* Completed Services Section - First */}
        <ServiceCard
          style={{
            flexDirection: "column",
            alignItems: "flex-start",
            padding: "12px 12px 8px 12px",
          }}
        >
          <Box display="flex" alignItems="center" gap={1} mb={1}>
            <ServiceIconContainer>
              <ServiceIcon src={services} alt="Service" />
            </ServiceIconContainer>
            <SectionTitle
              style={{
                marginBottom: 0,
                fontSize: "16px",
                fontWeight: 500,
                "@media (max-width: 768px)": {
                  fontSize: "10px",
                },
              }}
            >
              Services Completed
            </SectionTitle>
          </Box>

          {completedServices.map((service, index) => (
            <Box
              key={index}
              display="flex"
              justifyContent="space-between"
              alignItems="center"
              width="100%"
              mb={index !== completedServices.length - 1 ? 0.5 : 0}
              py={0.5}
              sx={{
                minHeight: { xs: "24px", sm: "28px" },
                borderBottom: index !== completedServices.length - 1 ? "1px solid #e9ecef" : "none",
                paddingBottom: index !== completedServices.length - 1 ? "4px" : "0",
              }}
            >
              <ServiceName style={{ fontSize: "12px" }}>{service.name}</ServiceName>
              <ServiceTime style={{ fontSize: "12px" }}>{formatFirebaseTimestamp(service.completedAt, "MMM DD, h:mm A")}</ServiceTime>
            </Box>
          ))}
        </ServiceCard>

        {/* Patient Status Section - New Design */}
        {(mood || appetite || mobility) && (
          <SectionContainer>
            {mood && (
              <StatusCard>
                <StatusIconContainer className="mood">
                  <StatusIcon src={MoodIcon} alt="Mood" />
                </StatusIconContainer>
                <StatusContent>
                  <StatusTitle>Mood</StatusTitle>
                  <StatusDescription style={{ fontSize: "12px" }}>{mood}</StatusDescription>
                </StatusContent>
              </StatusCard>
            )}

            {appetite && (
              <StatusCard>
                <StatusIconContainer className="appetite">
                  <StatusIcon src={AppetiteIcon} alt="Appetite" />
                </StatusIconContainer>
                <StatusContent>
                  <StatusTitle>Appetite</StatusTitle>
                  <StatusDescription style={{ fontSize: "12px" }}>{appetite}</StatusDescription>
                </StatusContent>
              </StatusCard>
            )}

            {mobility && (
              <StatusCard>
                <StatusIconContainer className="mobility">
                  <StatusIcon src={MobilityIcon} alt="Mobility" />
                </StatusIconContainer>
                <StatusContent>
                  <StatusTitle>Mobility</StatusTitle>
                  <StatusDescription style={{ fontSize: "12px" }}>{mobility}</StatusDescription>
                </StatusContent>
              </StatusCard>
            )}
          </SectionContainer>
        )}

        {/* Medications Section */}
        {medications.length > 0 && (
          <SectionContainer>
            <SectionTitle>Medications</SectionTitle>
            <Box>
              {medications.map((medication, index) => (
                <MedicationChip key={index} label={medication} size="small" />
              ))}
            </Box>
          </SectionContainer>
        )}

        {/* Patient Images Section */}
        {patientImages.length > 0 && (
          <SectionContainer>
            <SectionTitle>Patient Images</SectionTitle>
            {patientImages.length === 1 ? (
              // Single image - zoomed out (smaller size)
              <Box display="flex" justifyContent="center">
                <Box sx={{ width: '100%', maxWidth: '300px' }}>
                  <PatientImageWithLoading image={patientImages[0]} index={0} />
                </Box>
              </Box>
            ) : (
              // Multiple images - use grid layout
              <Grid container spacing={2}>
                {patientImages.map((image, index) => (
                  <Grid item xs={6} key={index}>
                    <PatientImageWithLoading image={image} index={index} />
                  </Grid>
                ))}
              </Grid>
            )}
          </SectionContainer>
        )}

        {/* Patient Signature Section - Last */}
        {patientSignature?.url && (
          <SectionContainer>
            <SectionTitle>Patient Signature</SectionTitle>
            <SignatureContainer style={{ marginBottom: 5 }}>
              <SignatureImage
                src={patientSignature.url}
                alt="Patient Signature"
                onClick={() => window.open(patientSignature.url, "_blank")}
              />
            </SignatureContainer>
          </SectionContainer>
        )}

        {/* Empty State */}
        {!mood &&
          !appetite &&
          !mobility &&
          medications.length === 0 &&
          patientImages.length === 0 &&
          completedServices.length === 0 &&
          !patientSignature?.url && (
            <Box textAlign="center" py={4}>
              <Typography color="textSecondary">No visit notes available</Typography>
            </Box>
          )}
      </SidebarContainer>
    </Drawer>
  );
};

export default VisitNotesSidebar;
