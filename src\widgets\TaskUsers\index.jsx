import React from "react";
import Widget from "@components/Widget";
import WidgetBody from "@components/Widget/WidgetBody";
import WidgetHeader from "@components/Widget/WidgetHeader";
import CustomSelect from "@ui/Select";
import { useDispatch, useSelector } from "react-redux";
import Avatar from "@ui/Avatar";
import { getNameInitials } from "@utils/helpers";
import { useState } from "react";
import styled from "styled-components";
import { Box } from "@mui/material";
import { textSizes } from "@styles/vars";
import { useEffect } from "react";
import { updateCurrentUserAction } from "@store/slices/tasks";

const Label = styled.label`
  font-size: ${textSizes["14"]};
  width: fit-content;
  margin-bottom: 8px;
  display: block;
`;

const TaskUsers = () => {
  const dispatch = useDispatch();
  const { caregivers, clients, nurses } = useSelector((state) => state.users);
  const { currentNurse, currentCaregiver, currentPatient, tasks } = useSelector((state) => state.tasks);
  const current_client_data = clients?.find((item) => item?.id === currentPatient);

  const filtered_caregivers = caregivers?.filter((item) => item?.nurse === currentNurse);

  // useEffect(() => {
  //   if (!currentNurse) {
  //     dispatch(updateCurrentUserAction(nurses[0]));
  //   }
  // }, [nurses]);

  useEffect(() => {
    if (!currentPatient) {
      // dispatch(updateCurrentUserAction(clients[0]));
    }
  }, [clients]);

  useEffect(() => {
    if (currentPatient) {
      const client_caregiver = caregivers?.find((item) => item?.id === current_client_data?.assignedCaregiver);
      if (client_caregiver) {
        // dispatch(updateCurrentUserAction(client_caregiver));
      } else {
        // dispatch(updateCurrentUserAction({ role: "CAREGIVER", id: null }));
      }
      const client_nurse = nurses?.find((item) => item?.id === current_client_data?.assignedNurse);
      if (client_nurse) {
        // dispatch(updateCurrentUserAction(client_nurse));
      } else {
        // dispatch(updateCurrentUserAction({ role: "NURSE", id: null }));
      }
    }
  }, [currentPatient, tasks]);

  const createOptions = (arr) => {
    let options = [];
    arr?.forEach((item) => {
      options.push({
        ...item,
        value: item?.id,
        label: (
          <div className="user-option">
            <Avatar
              avatar={{ jpg: item?.photo?.url }}
              alt={item?.name}
              size={40}
              initals={getNameInitials(item?.firstName, item?.lastName)}
            />
            <span>{item?.firstName + " " + item?.lastName}</span>
          </div>
        ),
      });
    });
    return options;
  };

  const SelectUser = ({ label, options, value, onChangeOption, placeholder, disabled }) => {
    return (
      <Box mt={2}>
        <Label>{label}</Label>
        <CustomSelect
          options={options}
          value={value}
          variant="user"
          changeHandler={(val) => onChangeOption(val)}
          placeholder={placeholder}
          disabled={disabled}
        />
      </Box>
    );
  };

  function onSelectUser(user) {
    const { label, ...rest } = user;
    // dispatch(updateCurrentUserAction(rest));
  }

  return (
    <>
      <Widget name="TaskUsers" mobile={600}>
        <WidgetHeader title="Select Patient"></WidgetHeader>
        <WidgetBody>
          <SelectUser
            label="Patient"
            placeholder="Select Patient"
            onChangeOption={onSelectUser}
            options={createOptions(clients)}
            value={createOptions(clients)?.find((item) => item?.id === currentPatient)}
          />
          <SelectUser
            label="Caregiver"
            placeholder="No Caregiver"
            onChangeOption={onSelectUser}
            options={createOptions(filtered_caregivers)}
            value={createOptions(filtered_caregivers)?.find((item) => item?.id === currentCaregiver)}
            disabled
          />
          <SelectUser
            label="Nurse"
            placeholder="No Nurse"
            onChangeOption={onSelectUser}
            options={createOptions(nurses)}
            value={createOptions(nurses)?.find((item) => item?.id === currentNurse)}
            disabled
          />
        </WidgetBody>
      </Widget>
    </>
  );
};

export default TaskUsers;
