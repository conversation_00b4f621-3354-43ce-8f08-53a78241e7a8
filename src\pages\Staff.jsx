// components
import { depsOptions } from "@constants/options";
import Page from "@layout/Page";
import StaffList from "@widgets/StaffList";
import { useState } from "react";
import { useNavigate } from "react-router";

const Staff = () => {
  const navigate = useNavigate();
  const [category, setCategory] = useState(depsOptions[0]);

  const selectedRole = () => {
    switch (category.value) {
      case "all":
        return { btnText: "Add User", url: "/add_staff" };
      case "nurse":
        return { btnText: "Add User", url: "/add_staff"  };
      case "caregiver":
        return { btnText: "Add User", url: "/add_staff" };
      default:
        return { btnText: "Add User", url: "/add_staff" };
    }
  };

  return (
    <Page
      title="Staff List"
      btnText={selectedRole()?.btnText}
      showRightBtn
      onClickBtn={() => navigate(selectedRole()?.url)}
    >
      <StaffList variant="staff" category={category} setCategory={setCategory} />
    </Page>
  );
};

export default Staff;
