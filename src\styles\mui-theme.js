import { createTheme } from "@mui/material/styles";
export const theme = createTheme({
  palette: {
    mode: "light",
    primary: {
      main: "#00898C",
      light: "#33a1a3",
      dark: "#006162",
      contrastText: "#ffffff",
    },
    secondary: {
      main: "#929292",
      light: "#c5c5c5",
      dark: "#e0e0e0",
    },
    error: {
      main: "#D32F2F",
      light: "#ef5350",
      dark: "#c62828",
      contrastText: "#ffffff",
    },
    warning: {
      main: "#ED6C02",
      light: "#ff9800",
      dark: "#e65100",
      contrastText: "#ffffff",
    },
    info: {
      main: "#0288D1",
      light: "#03a9f4",
      dark: "#01579b",
      contrastText: "#ffffff",
    },
    success: {
      main: "#2E7D32",
      light: "#4caf50",
      dark: "#1b5e20",
      contrastText: "#ffffff",
    },
    background: {
      default: "#f5f5f5",
      paper: "#f1f5f8",
    },
    text: {
      primary: "#1A1A1A",
      secondary: "#4f4f4f",
      disabled: "#9e9e9e",
    },
  },
  typography: {
    fontFamily: ["Poppins", "Helvetica", "Arial", "sans-serif"].join(","),
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: { boxShadow: "none", ":hover": { boxShadow: "none" } },
      },
    },
    MuiButtonBase: { styleOverrides: { root: { boxShadow: "none", ":hover": { boxShadow: "none" } } } },
  },
});
