import { useState } from "react";
// styled components
import { Container } from "./style";
import { Input } from "@ui/Field";
import DateInput from "@components/MaskedInputs/Date";

// components
import Btn from "@ui/Btn";
import ModalWindow from "@components/ModalWindow";
import LabeledFormInput from "@ui/LabeledFormInput";
import CustomSelect from "@ui/Select";
import countryList from "react-select-country-list";
import { breakpoints } from "@styles/vars";
import styled from "styled-components";
import { Box, Checkbox, Divider, FormControlLabel, FormLabel, TextField } from "@mui/material";
import { VisibilityOff, Visibility, CheckBox as CheckboxIcon, CheckBoxOutlineBlank } from "@mui/icons-material";
import TextArea from "@ui/TextArea/TextArea";

const InnerContainer = styled.div`
  display: grid;
  grid-gap: 16px;
  margin: 24px 0;

  ${breakpoints.landscapeS} {
    grid-template-columns: 1fr 1fr;
  }
`;

const SHIFT_LENGTH = [
  { value: 1, label: "1 hour" },
  { value: 2, label: "2 hours" },
  { value: 3, label: "3 hours" },
  { value: 4, label: "4 hours" },
  { value: 5, label: "5 hours" },
  { value: 6, label: "6 hours" },
  { value: 7, label: "7 hours" },
  { value: 8, label: "8 hours" },
  { value: 9, label: "9 hours" },
  { value: 10, label: "10 hours" },
  { value: 11, label: "11 hours" },
  { value: 12, label: "12 hours" },
];

const TIME_OPTIONS = [
  { value: "06:00", label: "6:00 AM" },
  { value: "07:00", label: "7:00 AM" },
  { value: "08:00", label: "8:00 AM" },
  { value: "09:00", label: "9:00 AM" },
  { value: "10:00", label: "10:00 AM" },
  { value: "11:00", label: "11:00 AM" },
  { value: "12:00", label: "12:00 PM" },
  { value: "13:00", label: "1:00 PM" },
  { value: "14:00", label: "2:00 PM" },
  { value: "15:00", label: "3:00 PM" },
  { value: "16:00", label: "4:00 PM" },
  { value: "17:00", label: "5:00 PM" },
  { value: "18:00", label: "6:00 PM" },
  { value: "19:00", label: "7:00 PM" },
  { value: "20:00", label: "8:00 PM" },
  { value: "21:00", label: "9:00 PM" },
  { value: "22:00", label: "10:00 PM" },
];

const RECURRENCE = [
  { value: "daily", label: "Daily" },
  { value: "weekly", label: "Weekly" },
  { value: "monthly", label: "Monthly" },
  { value: "flexible", label: "Flexible" },
  { value: "60-days", label: "60 Days" },
];
const CAREGIVERS = [
  { value: "user-1", label: "John Doe" },
  { value: "user-2", label: "Steve Smith" },
  { value: "user-3", label: "David Warner" },
  { value: "user-4", label: "Pat Cummins" },
  { value: "user-5", label: "Mitcheal Stark" },
];

const DoctorPopup = ({ name, open, handler, elemsHeight }) => {
  const [selectedCountry, setSelectedCountry] = useState();
  const [selectedCity, setSelectedCity] = useState();
  const [cities, setCities] = useState([]);

  const getCountriesOptions = () => {
    let countries = countryList().getData();
    for (let i = 0; i < countries.length; i++) {
      if (countries[i].value === "RU") {
        countries[i].label = "Russia [terrorist state]";
      }
    }
    return countries;
  };

  const handleCountryChange = (country) => {
    setSelectedCountry(country);
    setSelectedCity(null);
    let options = [];
    // const rawData = City.getCitiesOfCountry(country.value);
    // rawData.map((item) => options.push({ value: item.name, label: item.name }));
    // setCities(options);
  };

  return (
    <ModalWindow isVisible={open} visibilityHandler={handler}>
      <Container className={open ? "visible" : ""} top={0}>
        <div className="header">
          <div className="user">{name}</div>
        </div>
        <Divider sx={{ mt: 2 }} />

        <InnerContainer>
          <Box sx={{ gridColumn: "1/3" }}>
            <LabeledFormInput
              id={`shift-length`}
              title="Shift Length"
              placeholder=""
              customInput={
                <CustomSelect
                  label={`Shift Length`}
                  placeholder=""
                  options={SHIFT_LENGTH}
                  // value={selectedCountry}
                  variant="basic"
                  changeHandler={(e) => {}}
                />
              }
            />
          </Box>
          <Divider sx={{ m: 0, gridColumn: "1/3" }} />

          <FormControlLabel
            sx={{ fontSize: 16 }}
            label={<>Override Shift Length</>}
            control={
              <Checkbox
                color="primary"
                checkedIcon={<CheckboxIcon />}
                icon={<CheckBoxOutlineBlank fill="#2D2D2D" opacity={0.5} />}
              />
            }
          />
          <FormControlLabel
            sx={{ fontSize: 16 }}
            label={<>Schedule without Time</>}
            control={
              <Checkbox
                color="primary"
                checkedIcon={<CheckboxIcon />}
                icon={<CheckBoxOutlineBlank fill="#2D2D2D" opacity={0.5} />}
              />
            }
          />
          <Divider sx={{ m: 0, gridColumn: "1/3" }} />

          <Box sx={{
            gridColumn: "1/3",
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            flexDirection: { xs: "column", md: "row" },
            gap: { xs: 0.5, md: 0 }
          }}>
            <FormLabel sx={{ display: { xs: "block", md: "inline" } }}>From</FormLabel>
            <Box display="flex" columnGap={1} sx={{
              minWidth: { xs: "100%", md: "300px" },
              flexDirection: { xs: "column", sm: "row" }
            }}>
              <Box sx={{ flex: 1, minWidth: "140px" }}>
                <Input as={DateInput} id={`from-date`} />
              </Box>
              <Box sx={{ flex: 1, minWidth: "140px" }}>
                <CustomSelect
                  label="Start Time"
                  placeholder="Select start time"
                  options={TIME_OPTIONS}
                  variant="basic"
                  changeHandler={(e) => {}}
                  isSearchable={false}
                />
              </Box>
            </Box>
          </Box>

          <Box sx={{
            gridColumn: "1/3",
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            flexDirection: { xs: "column", md: "row" },
            gap: { xs: 0.5, md: 0 }
          }}>
            <FormLabel sx={{ display: { xs: "block", md: "inline" } }}>To</FormLabel>
            <Box display="flex" columnGap={1} sx={{
              minWidth: { xs: "100%", md: "300px" },
              flexDirection: { xs: "column", sm: "row" }
            }}>
              <Box sx={{ flex: 1, minWidth: "140px" }}>
                <Input as={DateInput} id={`to-date`} />
              </Box>
              <Box sx={{ flex: 1, minWidth: "140px" }}>
                <CustomSelect
                  label="End Time"
                  placeholder="Select end time"
                  options={TIME_OPTIONS}
                  variant="basic"
                  changeHandler={(e) => {}}
                  isSearchable={false}
                />
              </Box>
            </Box>
          </Box>

          <Box sx={{ gridColumn: "1/3" }}>
            <LabeledFormInput
              id={`Recurrence`}
              title="Recurrence"
              placeholder=""
              customInput={
                <CustomSelect
                  label={`Recurrence`}
                  placeholder=""
                  options={RECURRENCE}
                  // value={selectedCountry}
                  variant="basic"
                  changeHandler={(e) => {}}
                />
              }
            />
          </Box>

          {/* CAREGIVER */}
          <Box sx={{ gridColumn: "1/3" }}>
            <LabeledFormInput
              id={`Caregiver`}
              title="Caregiver"
              placeholder=""
              customInput={
                <CustomSelect
                  label={`Caregiver/Employee`}
                  placeholder=""
                  options={CAREGIVERS}
                  // value={selectedCountry}
                  variant="basic"
                  changeHandler={(e) => {}}
                />
              }
            />
          </Box>

          {/* COMMENT */}
          <Box sx={{ gridColumn: "1/3" }}>
            <TextArea placeholder="Comments" />
          </Box>

          {/* Buttons */}
          <Box sx={{ gridColumn: "1/3", display: "flex", columnGap: 1 }}>
            <Btn text="Save & Close" handler={() => handler(false)} />
            <Btn text="Save & Add Another" handler={() => handler(false)} />
            <Btn text="Cancel" handler={() => handler(false)} />
          </Box>
        </InnerContainer>
      </Container>
    </ModalWindow>
  );
};

export default DoctorPopup;
