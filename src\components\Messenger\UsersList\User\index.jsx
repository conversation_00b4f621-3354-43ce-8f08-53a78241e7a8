// styled components
import { UserItem } from "@components/Messenger/UsersList/User/style";

// components
import Avatar from "@ui/Avatar";
import { QtyBadge } from "@ui/Badge/style";
import { getNameInitials } from "@utils/helpers";
import PropTypes from "prop-types";
import { Badge } from "@ui/Badge/style";

import { useSelector } from "react-redux";
import { doc, updateDoc, collection, query, where, getDocs, writeBatch } from "firebase/firestore";
import { db } from "@config/firebase.config";
import { COLLECTIONS } from "@constants/app";
import moment from "moment";

const User = ({ data, user, onUserSelect, setModal, lastMessage, activeChat, chat }) => {
  const { firstName, lastName, isTyping, photo } = data;
  const { user: loggedInUser } = useSelector((state) => state.auth);
  const smallScreen = window?.matchMedia("(max-width: 1038.98px)").matches;

  const handleClick = async () => {
    try {
      if (lastMessage?.read === false && lastMessage?.senderId !== loggedInUser?.id) {
        // Update the lastMessage read status in the chat document
        const chatRef = doc(db, COLLECTIONS.CHATS, chat?.id);
        await updateDoc(chatRef, {
          "lastMessage.read": true,
        });

        // Also update the unread count for the current user
        const unreadCountField = `unreadCounts.${loggedInUser?.id}`;
        await updateDoc(chatRef, {
          [unreadCountField]: 0,
        });
      }

      onUserSelect(chat?.id);
      smallScreen && setModal(true);
    } catch (error) {
      console.error("Error marking messages as read:", error);
      // Still proceed with chat selection even if marking as read fails
      onUserSelect(chat?.id);
      smallScreen && setModal(true);
    }
  };

  const getPreviewContent = () => {
    if (isTyping) {
      return (
        <span className="preview">
          <i className="icon icon-pen" /> Typing...
        </span>
      );
    }

    if (!lastMessage) return null;

    if (lastMessage.audio) {
      return (
        <span className="preview">
          <i className="icon icon-play" /> Audio message
        </span>
      );
    }

    if (lastMessage.image?.url) {
      return (
        <span className="preview">
          <i className="icon icon-image" /> Photo
        </span>
      );
    }

    // Ensure we're rendering a string, not an object
    const messageText = typeof lastMessage.text === 'string' 
      ? lastMessage.text 
      : lastMessage.text?.text || lastMessage.message || 'Message';

    return <span className="preview">{messageText}</span>;
  };

  const getMessageTime = () => {
    if (!lastMessage?.createdAt) return null;

    const messageTime = lastMessage.createdAt.toDate
      ? moment(lastMessage.createdAt.toDate())
      : moment(lastMessage.createdAt);

    const now = moment();

    // If message is from today, show time (e.g., "2:30 PM")
    if (messageTime.isSame(now, "day")) {
      return messageTime.format("h:mm A");
    }

    // If message is from yesterday, show "Yesterday"
    if (messageTime.isSame(now.clone().subtract(1, "day"), "day")) {
      return "Yesterday";
    }

    // If message is from this week, show day name (e.g., "Monday")
    if (messageTime.isSame(now, "week")) {
      return messageTime.format("dddd");
    }

    // If message is older, show date (e.g., "12/25")
    return messageTime.format("M/D");
  };

  const shouldShowUnread = lastMessage?.read === false && lastMessage?.senderId !== loggedInUser?.id;

  return (
    <UserItem className={activeChat === chat?.id ? "active" : ""} onClick={handleClick} hasUnread={shouldShowUnread}>
      <div className="container">
        <div className="main">
          <Avatar
            avatar={{ jpg: photo?.url }}
            alt={`${firstName} ${lastName}`}
            initals={getNameInitials(firstName, lastName)}
            online={data.online}
          />
          <div className="main_wrapper">
            <div className="user_info">
              <span className="name">
                {firstName} {lastName}
              </span>
              {getMessageTime() && <span className="time">{getMessageTime()}</span>}
            </div>
            {getPreviewContent()}
          </div>

          {shouldShowUnread && (
            <Badge
              className="badge"
              color="yellow"
              style={{
                position: "absolute",
                top: "-6px",
                right: "-6px",
                zIndex: 2,
              }}
            />
          )}
        </div>
      </div>
    </UserItem>
  );
};

User.propTypes = {
  data: PropTypes.object.isRequired,
  onUserSelect: PropTypes.func.isRequired,
  setModal: PropTypes.func,
  lastMessage: PropTypes.object,
  activeChat: PropTypes.string,
  chat: PropTypes.object,
};

export default User;
