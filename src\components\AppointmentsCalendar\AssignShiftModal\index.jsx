// styled components
import { Container } from "./style";
import { Input } from "@ui/Field";
import DateInput from "@components/MaskedInputs/Date";

// components
import Btn from "@ui/Btn";
import ModalWindow from "@components/ModalWindow";
import LabeledFormInput from "@ui/LabeledFormInput";
import CustomSelect from "@ui/Select";
import { breakpoints } from "@styles/vars";
import styled from "styled-components";
import { Box, Checkbox, Divider, FormControlLabel, FormLabel, Typography } from "@mui/material";
import { CheckBox as CheckboxIcon, CheckBoxOutlineBlank } from "@mui/icons-material";
import TextArea from "@ui/TextArea/TextArea";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useState, useRef } from "react";
import moment from "moment";
import { doc, setDoc, Timestamp } from "firebase/firestore";
import { getFunctions, httpsCallable } from "firebase/functions";
import { useDispatch, useSelector } from "react-redux";
import { nanoid } from "nanoid";
import { db } from "@config/firebase.config";
import { COLLECTIONS } from "@constants/app";
import { addNewAppointmentAction, addMultipleAppointmentsAction } from "@store/slices/appointments";
// Removed calculateNextVisit import - now handled by cloud functions
import { useSnackbar } from "notistack";
import {
  checkAppointmentConflict,
  formatConflictErrorMessage,
  checkForExactDuplicate,
  checkCaregiverAppointmentConflict,
  formatCaregiverConflictErrorMessage,
} from "@utils/appointmentValidation";
import { collection, getDocs, query, where, getDoc } from "firebase/firestore";

const InnerContainer = styled.div`
  display: grid;
  grid-gap: 16px;
  margin: 24px 0;

  ${breakpoints.mobileS} {
    grid-gap: 12px;
    margin: 16px 0;
  }

  ${breakpoints.landscapeS} {
    grid-template-columns: 1fr 1fr;
  }
`;

const SHIFT_LENGTH = [
  { value: 1, label: "1 hour" },
  { value: 2, label: "2 hours" },
  { value: 3, label: "3 hours" },
  { value: 4, label: "4 hours" },
  { value: 5, label: "5 hours" },
  { value: 6, label: "6 hours" },
  { value: 7, label: "7 hours" },
  { value: 8, label: "8 hours" },
  { value: 9, label: "9 hours" },
  { value: 10, label: "10 hours" },
  { value: 11, label: "11 hours" },
  { value: 12, label: "12 hours" },
];

const TIME_OPTIONS = [
  { value: "00:00", label: "12:00 AM" },
  { value: "01:00", label: "1:00 AM" },
  { value: "02:00", label: "2:00 AM" },
  { value: "03:00", label: "3:00 AM" },
  { value: "04:00", label: "4:00 AM" },
  { value: "05:00", label: "5:00 AM" },
  { value: "06:00", label: "6:00 AM" },
  { value: "07:00", label: "7:00 AM" },
  { value: "08:00", label: "8:00 AM" },
  { value: "09:00", label: "9:00 AM" },
  { value: "10:00", label: "10:00 AM" },
  { value: "11:00", label: "11:00 AM" },
  { value: "12:00", label: "12:00 PM" },
  { value: "13:00", label: "1:00 PM" },
  { value: "14:00", label: "2:00 PM" },
  { value: "15:00", label: "3:00 PM" },
  { value: "16:00", label: "4:00 PM" },
  { value: "17:00", label: "5:00 PM" },
  { value: "18:00", label: "6:00 PM" },
  { value: "19:00", label: "7:00 PM" },
  { value: "20:00", label: "8:00 PM" },
  { value: "21:00", label: "9:00 PM" },
  { value: "22:00", label: "10:00 PM" },
  { value: "23:00", label: "11:00 PM" },
];

const RECURRENCE = [
  { value: "daily", label: "Daily" },
  { value: "weekly", label: "Weekly" },
  { value: "monthly", label: "Monthly" },
];

const SERVICE_OPTIONS = [
  { label: "Auxiliary Nurse", value: "auxiliary_nurse" },
  { label: "Home Health Aide", value: "home_health_aide" },
  { label: "Personal Support Worker", value: "personal_support_worker" },
  { label: "Palliative Caregiver", value: "palliative_caregiver" },
  { label: "Dementia Caregiver", value: "dementia_caregiver" },
];

const shiftSchema = z
  .object({
    shiftLength: z.coerce.number().optional(),
    overrideShift: z.boolean().default(false),
    caregiver: z.string().min(1, "Caregiver is required"),
    scheduleWithoutTime: z.boolean(),
    recurrence: z.string().optional(),
    recurrenceEndDate: z.string().optional(),
    startDate: z.string().min(1, "Start date is required"),
    endDate: z.string().min(1, "End date is required"),
    serviceType: z.string().min(1, "Service Type is required"),
    startTime: z.string().optional(),
    endTime: z.string().optional(),
    isFromCalendar: z.boolean().default(false),
    comments: z.string().optional(),
    submission_type: z.string().optional(),
  })
  .superRefine((data, ctx) => {
    // Condition: shiftLength is required if overrideShift is false
    if (!data.overrideShift) {
      if (data.shiftLength === undefined || isNaN(data.shiftLength)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Shift length is required",
          path: ["shiftLength"],
        });
      }
    }

    // Condition: startTime and endTime are required and valid if scheduleWithoutTime is false
    if (!data.scheduleWithoutTime) {
      if (!data.startTime || data.startTime.trim() === "") {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Start time is required",
          path: ["startTime"],
        });
      }

      if (!data.endTime || data.endTime.trim() === "") {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "End time is required",
          path: ["endTime"],
        });
      }
      if (data.startTime && data.endTime) {
        const startDateTime = moment(data.startTime, "HH:mm");
        const endDateTime = moment(data.endTime, "HH:mm");
      }
    }

    // Condition: Date must be within allowed range
    if (data.startDate) {
      const selectedDate = moment(data.startDate);
      const threeMonthsFromNow = moment().add(2, "months").endOf("month");

      if (selectedDate.isAfter(threeMonthsFromNow)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Please select a valid date",
          path: ["startDate"],
        });
      }
    }
  });

const AssignShiftModal = ({ name, open, handler, date, mode, defaultValues, commonNurse }) => {
  const isFromCalendar = mode === "caregiver_calendar";

  const dispatch = useDispatch();
  const { enqueueSnackbar } = useSnackbar();

  const { caregivers, clients } = useSelector((state) => state.users);
  const { user } = useSelector((state) => state.auth);

  // State to prevent duplicate submissions
  const [isFormSubmitting, setIsFormSubmitting] = useState(false);
  const submissionInProgress = useRef(false);
  const lastSubmissionTime = useRef(0);
  const SUBMISSION_DEBOUNCE_MS = 2000; // 2 seconds debounce

  // Get the nurse ID - use commonNurse prop, or current user if they're a nurse, or find from client
  const getNurseId = () => {
    if (user?.role === "NURSE") return user.id;
    if (defaultValues?.client) {
      const client = clients?.find((c) => c.id === defaultValues.client);
      return client?.assignedNurse;
    }
    return null;
  };

  const {
    handleSubmit,
    register,
    setValue,
    setError,
    watch,
    reset,
    formState: { errors, isSubmitting },
  } = useForm({
    defaultValues: {
      overrideShift: false,
      scheduleWithoutTime: false,
      startDate: "",
      endDate: "",
      startTime: "",
      endTime: "",
      recurrence: "",
      recurrenceEndDate: "",
      serviceType: "",
      caregiver: "",
      comments: "",
      submission_type: "",
    },
    resolver: zodResolver(shiftSchema),
  });
  const caregivers_options = caregivers
    ?.filter((item) => item?.type === watch("serviceType"))
    ?.map((item) => ({ label: item?.name, value: item?.id }));
  function onCloseModal() {
    handler(false);
    reset({
      overrideShift: false,
      scheduleWithoutTime: false,
      startDate: "",
      endDate: "",
      startTime: "",
      endTime: "",
      recurrence: "",
      recurrenceEndDate: "",
      serviceType: "",
      caregiver: "",
      comments: "",
      submission_type: "",
    });
    // Reset submission state when modal closes
    setIsFormSubmitting(false);
    submissionInProgress.current = false;
    lastSubmissionTime.current = 0;
  }
  function onSelectShiftLength(val) {
    setValue("shiftLength", Number(val));
    setError("shiftLength", { message: undefined });
    if (watch("startTime") && !watch("overrideShift")) {
      const startDate = moment(watch("startDate"));
      const startTime = moment(watch("startTime"), "HH:mm");
      const endTime = startTime.clone().add(val, "hours");

      // Handle cross-midnight: if end time goes past 23:59, wrap to next day
      if (endTime.hour() < startTime.hour() || endTime.date() !== startTime.date()) {
        setValue("endTime", endTime.format("HH:mm"));
        // Update end date to next day for cross-midnight appointments
        const nextDay = startDate.clone().add(1, "day").format("YYYY-MM-DD");
        setValue("endDate", nextDay);
      } else {
        setValue("endTime", endTime.format("HH:mm"));
        // Keep same date for same-day appointments
        setValue("endDate", startDate.format("YYYY-MM-DD"));
      }
    }
  }
  function onSelectStartTime(selectedOption) {
    const timeValue = selectedOption?.value;
    setValue("startTime", timeValue);
    setError("startTime", { message: undefined });
    if (watch("shiftLength") && !watch("overrideShift") && timeValue) {
      const startDate = moment(watch("startDate"));
      const startTime = moment(timeValue, "HH:mm");
      const endTime = startTime.clone().add(watch("shiftLength"), "hours");
      setValue("endTime", endTime.format("HH:mm"));
      setError("endTime", { message: undefined });
      
      // Handle cross-midnight: update end date if appointment crosses midnight
      if (endTime.hour() < startTime.hour() || endTime.date() !== startTime.date()) {
        // Cross-midnight appointment - set end date to next day
        const nextDay = startDate.clone().add(1, "day").format("YYYY-MM-DD");
        setValue("endDate", nextDay);
      } else {
        // Same-day appointment - keep same date
        setValue("endDate", startDate.format("YYYY-MM-DD"));
      }
    }
  }

  function onSelectEndTime(selectedOption) {
    const timeValue = selectedOption?.value;
    setValue("endTime", timeValue);
    setError("endTime", { message: undefined });
    
    // Check if this creates a cross-midnight scenario
    const startTime = watch("startTime");
    if (startTime && timeValue) {
      const startDate = moment(watch("startDate"));
      const startMoment = moment(startTime, "HH:mm");
      const endMoment = moment(timeValue, "HH:mm");
      
      // If end time is before start time, it's a cross-midnight appointment
      if (endMoment.isBefore(startMoment) || endMoment.isSame(startMoment)) {
        // Cross-midnight appointment - set end date to next day
        const nextDay = startDate.clone().add(1, "day").format("YYYY-MM-DD");
        setValue("endDate", nextDay);
      } else {
        // Same-day appointment - keep same date
        setValue("endDate", startDate.format("YYYY-MM-DD"));
      }
    }
  }

  async function submitForm(formValues) {
    const currentTime = Date.now();

    // Prevent duplicate submissions with debounce
    if (isFormSubmitting || submissionInProgress.current) {
      console.log("Submission already in progress, ignoring duplicate request");
      return;
    }

    // Debounce rapid successive submissions
    if (currentTime - lastSubmissionTime.current < SUBMISSION_DEBOUNCE_MS) {
      console.log("Submission too soon after last attempt, ignoring");
      return;
    }

    console.log(formValues);

    // Set submission state
    setIsFormSubmitting(true);
    submissionInProgress.current = true;
    lastSubmissionTime.current = currentTime;

    try {
      // * Create caregiver appointment
      if (formValues.caregiver) {
        // Check for patient appointment conflicts only if scheduling with specific time
        if (!formValues.scheduleWithoutTime && defaultValues?.client) {
          try {
            const conflictCheck = await checkAppointmentConflict(
              defaultValues.client,
              formValues.startDate,
              formValues.startTime,
              formValues.endTime,
              defaultValues?.id, // exclude current appointment if editing
              defaultValues?.refID, // exclude entire recurring series if editing
            );
            if (conflictCheck.hasConflict) {
              const errorMessage = formatConflictErrorMessage(conflictCheck.conflictingAppointments);
              setError("startTime", { type: "manual", message: errorMessage });
              enqueueSnackbar("Patient appointment conflict detected. Please choose a different time.", {
                variant: "error",
              });
              return;
            }
          } catch (error) {
            enqueueSnackbar(`Error validating patient appointment time: ${error.message}`, { variant: "error" });
            return;
          }
        } // Check for caregiver appointment conflicts only if scheduling with specific time
        if (!formValues.scheduleWithoutTime) {
          try {
            const caregiverConflictCheck = await checkCaregiverAppointmentConflict(
              formValues.caregiver,
              formValues.startDate,
              formValues.startTime,
              formValues.endTime,
              defaultValues?.id, // exclude current appointment if editing
            );
            if (caregiverConflictCheck.hasConflict) {
              const errorMessage = formatCaregiverConflictErrorMessage(caregiverConflictCheck.conflictingAppointments);
              setError("caregiver", { type: "manual", message: errorMessage });
              enqueueSnackbar("Caregiver scheduling conflict detected. Please choose a different caregiver or time.", {
                variant: "error",
              });
              return;
            }
          } catch (error) {
            enqueueSnackbar(`Error validating caregiver availability: ${error.message}`, { variant: "error" });
            return;
          }
        } // Check for exact duplicate appointments
        try {
          const isDuplicate = await checkForExactDuplicate(
            defaultValues?.client,
            formValues.caregiver,
            formValues.startDate,
            formValues.startTime,
            formValues.endTime,
            defaultValues?.id, // exclude current appointment if editing
          );
          if (isDuplicate) {
            enqueueSnackbar("An identical appointment already exists for this patient, caregiver, date and time.", {
              variant: "error",
            });
            return;
          }
        } catch (error) {
          console.error("Error checking for duplicate appointment:", error);
          // Continue with creation if duplicate check fails
        }

        const appointmentData = {
          nurse: getNurseId(),
          caregiver: formValues.caregiver,
          client: defaultValues?.client,
          serviceType: formValues.serviceType,
          status: "SCHEDULED",
          comments: formValues.comments,
        };

        // Handle recurrence if specified
        if (formValues.recurrence) {
          appointmentData.recurrence = {
            frequence: formValues.recurrence,
            interval: 1, // Default to every occurrence
          };

          if (formValues.recurrenceEndDate) {
            appointmentData.recurrenceEndDate = formValues.recurrenceEndDate;
          }

          // Add specific recurrence rules based on frequency
          if (formValues.recurrence === "weekly") {
            // Default to the same day of week as start date
            const startDay = moment(formValues.startDate).format("ddd").toLowerCase();
            appointmentData.recurrence.daysOfWeek = [startDay];
          } else if (formValues.recurrence === "monthly") {
            // Default to the same date of month as start date
            const startDate = moment(formValues.startDate).date().toString();
            appointmentData.recurrence.monthlyDates = [startDate];
          }
        }

        if (formValues.scheduleWithoutTime) {
          appointmentData.startDateTime = `${formValues.startDate} 00:00`;
          appointmentData.endDateTime = `${formValues.endDate} 23:59`;
          appointmentData.isScheduleWithTime = false;
        } else {
          appointmentData.isScheduleWithTime = true;
          appointmentData.startDateTime = `${formValues.startDate} ${formValues.startTime}`;

          // Handle cross-midnight appointments: if end time is before start time, it crosses midnight
          const startTime = moment(formValues.startTime, "HH:mm");
          const endTime = moment(formValues.endTime, "HH:mm");
          const isCrossMidnight = endTime.isBefore(startTime);

          if (isCrossMidnight) {
            // For cross-midnight appointments, end date is the next day
            const endDate = moment(formValues.startDate).add(1, "day").format("YYYY-MM-DD");
            appointmentData.endDateTime = `${endDate} ${formValues.endTime}`;
            appointmentData.isCrossMidnight = true; // Flag to identify cross-midnight appointments
          } else {
            appointmentData.endDateTime = `${formValues.endDate} ${formValues.endTime}`;
            appointmentData.isCrossMidnight = false;
          }
        }

        console.log("Appointment data:", appointmentData);

        // Call cloud function to generate appointments
        const functions = getFunctions();
        const generateRecurringAppointments = httpsCallable(functions, "generateRecurringAppointments");

        try {
          const result = await generateRecurringAppointments({ appointmentData });
          const { success, appointmentIds, count, recurrenceType, refID, recurrenceEndDate } = result.data;

          if (success) {
            // For recurring appointments, fetch all created appointments from Firebase and add to Redux
            if (count > 1 && appointmentIds && appointmentIds.length > 0) {
              try {
                let allCreatedAppointments = [];

                // Method 1: Try using refID to fetch all appointments in the series (more efficient)
                if (refID) {
                  try {
                    const refQuery = query(collection(db, COLLECTIONS.APPOINTMENTS), where("refID", "==", refID));
                    const refSnapshot = await getDocs(refQuery);
                    allCreatedAppointments = refSnapshot.docs.map((doc) => ({
                      id: doc.id,
                      ...doc.data(),
                    }));
                  } catch (refError) {
                    console.warn("Failed to fetch using refID, falling back to batch method:", refError);
                    allCreatedAppointments = []; // Reset for fallback
                  }
                }

                // Method 2: Fallback - fetch appointments by individual IDs in batches
                if (allCreatedAppointments.length === 0 && appointmentIds.length > 0) {
                  const batchSize = 10; // Firestore has a limit of 10 for 'in' queries

                  for (let i = 0; i < appointmentIds.length; i += batchSize) {
                    const batch = appointmentIds.slice(i, i + batchSize);
                    const batchQuery = query(collection(db, COLLECTIONS.APPOINTMENTS), where("__name__", "in", batch));
                    const batchSnapshot = await getDocs(batchQuery);
                    const batchAppointments = batchSnapshot.docs.map((doc) => ({
                      id: doc.id,
                      ...doc.data(),
                    }));

                    allCreatedAppointments.push(...batchAppointments);
                    console.log(`Fetched ${batchAppointments.length} appointments in this batch`);
                  }
                }

                if (allCreatedAppointments.length > 0) {
                  dispatch(addMultipleAppointmentsAction(allCreatedAppointments));
                } else {
                  console.warn("No appointments were fetched from Firebase, using fallback");
                  // Fallback: add just the base appointment data
                  const appointmentWithSeriesId = {
                    ...appointmentData,
                    refID,
                    isRecurringInstance: count > 1,
                  };
                  dispatch(addNewAppointmentAction(appointmentWithSeriesId));
                }
              } catch (fetchError) {
                console.error("Error fetching created appointments:", fetchError);

                // Fallback: add just the base appointment data
                const appointmentWithSeriesId = {
                  ...appointmentData,
                  refID,
                  isRecurringInstance: count > 1,
                };
                dispatch(addNewAppointmentAction(appointmentWithSeriesId));
              }
            } else {
              // Single appointment - fetch the created appointment from Firebase and add to Redux
              try {
                if (appointmentIds && appointmentIds.length > 0) {
                  const appointmentId = appointmentIds[0];
                  const appointmentDoc = await getDoc(doc(db, COLLECTIONS.APPOINTMENTS, appointmentId));

                  if (appointmentDoc.exists()) {
                    const createdAppointment = { id: appointmentDoc.id, ...appointmentDoc.data() };
                    dispatch(addNewAppointmentAction(createdAppointment));
                  } else {
                    console.warn("Single appointment document not found, using fallback");
                    // Fallback: add the base appointment data with the returned ID
                    const appointmentWithId = {
                      id: appointmentId,
                      ...appointmentData,
                      refID,
                      isRecurringInstance: count > 1,
                    };
                    dispatch(addNewAppointmentAction(appointmentWithId));
                  }
                } else {
                  console.warn("No appointment ID returned for single appointment, using fallback");
                  // Fallback: add just the base appointment data
                  const appointmentWithSeriesId = {
                    ...appointmentData,
                    refID,
                    isRecurringInstance: count > 1,
                  };
                  dispatch(addNewAppointmentAction(appointmentWithSeriesId));
                }
              } catch (fetchError) {
                console.error("Error fetching single appointment:", fetchError);

                // Fallback: add the base appointment data with ID if available
                if (appointmentIds && appointmentIds.length > 0) {
                  const appointmentWithId = {
                    id: appointmentIds[0],
                    ...appointmentData,
                    refID,
                    isRecurringInstance: count > 1,
                  };
                  dispatch(addNewAppointmentAction(appointmentWithId));
                } else {
                  const appointmentWithSeriesId = {
                    ...appointmentData,
                    refID,
                    isRecurringInstance: count > 1,
                  };
                  dispatch(addNewAppointmentAction(appointmentWithSeriesId));
                }
              }
            }

            let message;
            if (count > 1) {
              message = `Successfully created appointments`;
            } else {
              message = "New appointment added successfully";
            }

            enqueueSnackbar(message, { variant: "success" });

            if (watch("submission_type") === "add_another") {
              // Reset form for adding another appointment with a small delay to ensure state updates
              setTimeout(() => {
                reset({
                  overrideShift: false,
                  scheduleWithoutTime: false,
                  startDate: date ? moment(date).format("YYYY-MM-DD") : "",
                  endDate: date ? moment(date).format("YYYY-MM-DD") : "",
                  startTime: "",
                  endTime: "",
                  recurrence: "",
                  serviceType: "",
                  caregiver: "",
                  comments: "",
                  submission_type: "", // Clear the submission type
                });
                // Clear any form errors
                Object.keys(errors).forEach((field) => {
                  setError(field, { message: undefined });
                });
              }, 100);
            } else {
              onCloseModal();
            }
          } else {
            throw new Error("Failed to create appointments");
          }
        } catch (error) {
          console.error("Error creating appointments:", error);
          enqueueSnackbar(`Couldn't create the appointment: ${error.message}`, { variant: "error" });
          throw error; // Re-throw to be caught by outer try-catch
        }
      } else {
        enqueueSnackbar("Please select a caregiver", { variant: "warning" });
      }
    } catch (error) {
      console.error("Error in submitForm:", error);
      // Error handling is already done in individual catch blocks
    } finally {
      // Always reset submission state
      setIsFormSubmitting(false);
      submissionInProgress.current = false;
    }
  }

  useEffect(() => {
    if (date) {
      setValue("startDate", moment(date).format("YYYY-MM-DD"));
      setValue("endDate", moment(date).format("YYYY-MM-DD"));
    }
  }, [date]);

  useEffect(() => {
    if (watch("overrideShift") === false && watch("startTime")) {
      const startDate = moment(watch("startDate"));
      const startTime = moment(watch("startTime"), "HH:mm");
      const endTime = startTime.clone().add(watch("shiftLength"), "hours");
      setValue("endTime", endTime.format("HH:mm"));
      
      // Handle cross-midnight: update end date if appointment crosses midnight
      if (endTime.hour() < startTime.hour() || endTime.date() !== startTime.date()) {
        // Cross-midnight appointment - set end date to next day
        const nextDay = startDate.clone().add(1, "day").format("YYYY-MM-DD");
        setValue("endDate", nextDay);
      } else {
        // Same-day appointment - keep same date
        setValue("endDate", startDate.format("YYYY-MM-DD"));
      }
    }
  }, [watch("overrideShift")]);

  useEffect(() => {
    if (watch("scheduleWithoutTime")) {
      setValue("starTime", "");
      setValue("endTime", "");
    }
  }, [watch("scheduleWithoutTime")]);

  useEffect(() => {
    if (open && isFromCalendar) {
      setValue("isFromCalendar", isFromCalendar);
      setValue("caregiver", defaultValues?.caregiver);
    }
  }, [open]);

  // Auto-detect cross-midnight appointments and update end date
  useEffect(() => {
    const startTime = watch("startTime");
    const endTime = watch("endTime");
    const startDate = watch("startDate");
    
    if (startTime && endTime && startDate && !watch("scheduleWithoutTime")) {
      const startMoment = moment(startTime, "HH:mm");
      const endMoment = moment(endTime, "HH:mm");
      
      // If end time is before or equal to start time, it's a cross-midnight appointment
      if (endMoment.isBefore(startMoment) || endMoment.isSame(startMoment)) {
        // Cross-midnight appointment - set end date to next day
        const currentEndDate = watch("endDate");
        const expectedNextDay = moment(startDate).add(1, "day").format("YYYY-MM-DD");
        
        if (currentEndDate !== expectedNextDay) {
          setValue("endDate", expectedNextDay);
        }
      } else {
        // Same-day appointment - ensure end date matches start date
        const currentEndDate = watch("endDate");
        const expectedSameDay = moment(startDate).format("YYYY-MM-DD");
        
        if (currentEndDate !== expectedSameDay) {
          setValue("endDate", expectedSameDay);
        }
      }
    }
  }, [watch("startTime"), watch("endTime"), watch("startDate"), watch("scheduleWithoutTime")]);

  return (
    <ModalWindow isVisible={open} visibilityHandler={onCloseModal}>
      <Container className={open ? "visible" : ""} top={-100}>
        {isFromCalendar && (
          <>
            <div className="header">
              <div className="user">{name}</div>
            </div>
            <Divider sx={{ mt: 2 }} />
          </>
        )}
        <form onSubmit={handleSubmit(submitForm)}>
          <InnerContainer>
            {/* SELECT SHIFT LENGTH */}
            <Box sx={{ gridColumn: "1/3" }}>
              <LabeledFormInput
                id={`shift-length`}
                title="Shift Length"
                placeholder=""
                customInput={
                  <CustomSelect
                    label={`Shift Length`}
                    placeholder=""
                    options={SHIFT_LENGTH}
                    value={SHIFT_LENGTH.find((item) => item.value === watch("shiftLength"))}
                    variant="basic"
                    changeHandler={(opt) => onSelectShiftLength(opt?.value)}
                    disabled={watch("overrideShift")}
                    key={watch("shiftLength")}
                  />
                }
              />
              {errors?.shiftLength?.message && (
                <Typography color="error" variant="caption">
                  {errors?.shiftLength?.message}
                </Typography>
              )}
            </Box>
            <Divider sx={{ m: 0, gridColumn: "1/3" }} />
            {/* OVERRIDE SHIFT LENGTH */}
            <FormControlLabel
              sx={{ fontSize: 16, gridColumn: "1/3" }}
              label={<>Override Shift Length</>}
              control={
                <Checkbox
                  color="primary"
                  checkedIcon={<CheckboxIcon />}
                  icon={<CheckBoxOutlineBlank fill="#2D2D2D" opacity={0.5} />}
                  value={watch("overrideShift")}
                  checked={watch("overrideShift")}
                  onChange={(e) => {
                    setValue("overrideShift", e?.target?.checked);
                  }}
                  key={watch("overrideShift")}
                />
              }
            />
            {/* SCHEDULE WITHOUT TIME */}
            {/* <FormControlLabel
              sx={{ fontSize: 16 }}
              label={<>Schedule without Time</>}
              control={
                <Checkbox
                  color="primary"
                  checkedIcon={<CheckboxIcon />}
                  icon={<CheckBoxOutlineBlank fill="#2D2D2D" opacity={0.5} />}
                  onChange={(e) => {
                    setValue("scheduleWithoutTime", e?.target?.checked);
                  }}
                  key={watch("scheduleWithoutTime")}
                />
              }
            /> */}
            <Divider sx={{ m: 0, gridColumn: "1/3" }} />
            {/* FROM DATE & TIME */}
            <Box
              sx={{
                gridColumn: "1/3",
                display: "flex",
                justifyContent: "space-between",
                alignItems: { xs: "flex-start", sm: "center" },
                flexDirection: { xs: "column", sm: "row" },
                gap: { xs: 0.5, md: 0 },
              }}
            >
              <FormLabel sx={{ display: { xs: "block", md: "inline" } }}>From</FormLabel>
              <Box
                display="flex"
                columnGap={1}
                flexDirection={{ xs: "column important", sm: "row" }}
                sx={{
                  width: { xs: "100%", sm: "auto" },
                  minWidth: 0, // Allow shrinking
                  "& > *": {
                    minWidth: 0, // Allow children to shrink
                    flex: { xs: "1 1 auto", sm: "0 0 auto" },
                  },
                }}
              >
                <DateInput
                  id="date"
                  minDate={new Date()}
                  maxDate={moment().add(2, "months").endOf("month").toDate()}
                  onChange={(val) => {
                    const formatted_val = moment(val).format("YYYY-MM-DD");
                    setValue("startDate", formatted_val);
                    setValue("endDate", formatted_val);
                  }}
                  value={watch("startDate")}
                  // key={watch("startDate")}
                />
                <CustomSelect
                  label="Start Time"
                  placeholder="Start time"
                  options={TIME_OPTIONS}
                  value={TIME_OPTIONS.find((item) => item.value === watch("startTime"))}
                  variant="basic"
                  changeHandler={onSelectStartTime}
                  disabled={watch("scheduleWithoutTime")}
                  isSearchable={false}
                  key={watch("startTime")}
                />
              </Box>
            </Box>
            {errors?.startTime?.message && (
              <Typography color="error" variant="caption">
                {errors?.startTime?.message}
              </Typography>
            )}
            {/* END DATE & TIME */}
            <Box
              sx={{
                gridColumn: "1/3",
                display: "flex",
                justifyContent: "space-between",
                alignItems: { xs: "flex-start", sm: "center" },
                flexDirection: { xs: "column", sm: "row" },
                gap: { xs: 0.5, md: 0 },
              }}
            >
              <FormLabel>To</FormLabel>
              <Box
                display="flex"
                columnGap={1}
                flexDirection={{ xs: "column important", sm: "row" }}
                sx={{
                  width: { xs: "100%", sm: "auto" },
                  minWidth: 0, // Allow shrinking
                  "& > *": {
                    minWidth: 0, // Allow children to shrink
                    flex: { xs: "1 1 auto", sm: "0 0 auto" },
                  },
                }}
              >
                <DateInput
                  id="endDate"
                  minDate={new Date()}
                  maxDate={moment().add(2, "months").endOf("month").toDate()}
                  value={watch("endDate")}
                  disabled
                  // key={watch("endDate")}
                />
                <CustomSelect
                  label="End Time"
                  placeholder="End time"
                  options={TIME_OPTIONS}
                  value={TIME_OPTIONS.find((item) => item.value === watch("endTime"))}
                  variant="basic"
                  changeHandler={onSelectEndTime}
                  disabled={watch("scheduleWithoutTime") || !watch("overrideShift")}
                  isSearchable={false}
                  key={watch("endTime")}
                />
              </Box>
            </Box>
            {errors?.endTime?.message && (
              <Typography color="error" variant="caption">
                {errors?.endTime?.message}
              </Typography>
            )}{" "}
            {/* RECURRENCE */}
            <Box sx={{ gridColumn: "1/3" }}>
              <LabeledFormInput
                id={`Recurrence`}
                title="Recurrence"
                placeholder=""
                customInput={
                  <CustomSelect
                    label={`Recurrence`}
                    placeholder=""
                    options={RECURRENCE}
                    variant="basic"
                    value={RECURRENCE.find((item) => item.value === watch("recurrence"))}
                    changeHandler={(opt) => {
                      setValue("recurrence", opt?.value);
                      setError("recurrence", { message: undefined });
                    }}
                    key={watch("recurrence")}
                  />
                }
              />
              {errors?.recurrence?.message && (
                <Typography color="error" variant="caption">
                  {errors?.recurrence?.message}
                </Typography>
              )}
            </Box>
            {/* RECURRENCE END DATE - Show only when recurrence is selected */}
            {watch("recurrence") && (
              <Box sx={{ gridColumn: "1/3" }}>
                <LabeledFormInput
                  id="recurrenceEndDate"
                  title="Recurrence End Date"
                  placeholder="Select end date for recurring appointments"
                  customInput={
                    <DateInput
                      id="recurrenceEndDate"
                      minDate={watch("startDate") ? moment(watch("startDate")).add(1, "day").toDate() : new Date()}
                      maxDate={moment().add(6, "months").endOf("month").toDate()}
                      onChange={(val) => {
                        const formatted_val = moment(val).format("YYYY-MM-DD");
                        setValue("recurrenceEndDate", formatted_val);
                        setError("recurrenceEndDate", { message: undefined });
                      }}
                      value={watch("recurrenceEndDate")}
                      placeholder="Select end date"
                    />
                  }
                />
                {errors?.recurrenceEndDate?.message && (
                  <Typography color="error" variant="caption">
                    {errors?.recurrenceEndDate?.message}
                  </Typography>
                )}
              </Box>
            )}
            {/* SERVICE TYPE */}
            <Box sx={{ gridColumn: "1/3" }}>
              <LabeledFormInput
                id={`ServiceType`}
                title="ServiceType"
                placeholder="Service Type"
                customInput={
                  <CustomSelect
                    label={`Service Type`}
                    placeholder="Service Type"
                    options={SERVICE_OPTIONS}
                    value={SERVICE_OPTIONS.find((item) => item.value === watch("serviceType"))}
                    variant="basic"
                    changeHandler={(opt) => {
                      setValue("serviceType", opt?.value);
                      setError("serviceType", { message: undefined });
                      setValue("caregiver", "");
                    }}
                    key={watch("serviceType")}
                    isSearchable={false}
                  />
                }
              />
              {errors?.serviceType?.message && (
                <Typography color="error" variant="caption">
                  {errors?.serviceType?.message}
                </Typography>
              )}
            </Box>
            {/* CAREGIVER */}
            <Box sx={{ gridColumn: "1/3" }}>
              <LabeledFormInput
                id={`Caregiver`}
                title="Caregiver"
                placeholder="Caregiver"
                customInput={
                  <CustomSelect
                    label={`Caregiver`}
                    placeholder="Caregiver"
                    options={caregivers_options}
                    value={caregivers_options?.find((item) => item?.value === watch("caregiver"))}
                    variant="basic"
                    changeHandler={(opt) => {
                      setValue("caregiver", opt?.value);
                      setError("caregiver", { message: undefined });
                    }}
                    key={watch("caregiver")}
                    isSearchable={false}
                  />
                }
              />
              {errors?.caregiver?.message && (
                <Typography color="error" variant="caption">
                  {errors?.caregiver?.message}
                </Typography>
              )}
            </Box>
            {/* COMMENTS */}
            <Box position="relative" height="fit-content" mt={2} sx={{ gridColumn: "1/3" }}>
              <TextArea
                placeholder="Comments"
                id="comments"
                value={watch("comments")}
                handler={(e) => {
                  setValue("comments", e?.target?.value);
                }}
              />
            </Box>
            {/* Buttons */}
            <Box
              sx={{
                gridColumn: "1/3",
                display: "flex",
                columnGap: 1,
                flexDirection: { xs: "column", sm: "row" },
                gap: { xs: 1, sm: 1 },
              }}
            >
              <Btn
                type="submit"
                text={isFormSubmitting ? "Saving..." : "Save & Close"}
                className={isSubmitting || isFormSubmitting ? "disabled" : ""}
                disabled={isSubmitting || isFormSubmitting}
              />

              <Btn
                type="button"
                text={isFormSubmitting ? "Saving..." : "Save & Add Another"}
                className={isSubmitting || isFormSubmitting ? "disabled" : ""}
                disabled={isSubmitting || isFormSubmitting}
                handler={() => {
                  if (!isFormSubmitting && !isSubmitting) {
                    setValue("submission_type", "add_another");
                    handleSubmit(submitForm)();
                  }
                }}
              />

              <Btn
                type="button"
                text="Cancel"
                handler={onCloseModal}
                className={isSubmitting || isFormSubmitting ? "disabled" : ""}
                disabled={isSubmitting || isFormSubmitting}
              />
            </Box>
          </InnerContainer>
        </form>
      </Container>
    </ModalWindow>
  );
};

export default AssignShiftModal;
