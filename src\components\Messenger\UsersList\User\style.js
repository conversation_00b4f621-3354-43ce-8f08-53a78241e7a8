import styled from 'styled-components/macro';
import theme from 'styled-theming';
import {flex, colors, textSizes, light, dark} from '@styles/vars';
import {rgba} from 'polished';
import {pen} from '@styles/keyframes';

const borderColor = theme('theme', {
    light: rgba(light.bodyBg, 0.5),
    dark: rgba('#4A4F54', 0.5),
});

export const UserItem = styled.div`
  margin: 2px;
  position: relative;
  cursor: pointer;

  &:not(:last-of-type) {
    border-bottom: 1px solid ${borderColor};
  }

  &:hover, &.active {
    .container {
      &:after {
        opacity: 1;
      }

      .main_wrapper .preview {
        &:after {
          opacity: 0;
        }

        &:before {
          opacity: 1;
        }
      }
    }
  }

  .container {
    padding: 20px 22px;
    display: flex;
    ${flex.between};
    position: relative;

    &:after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: ${theme('theme', {
        light: light.bodyBg,
        dark: dark.bodyBg
      })};
      border-radius: 8px;
      transition: opacity var(--transition);
      z-index: 1;
      opacity: 0;
    }

    .qty-badge {
      position: absolute;
      right: 20px;
    }

    .main {
      display: flex;
      align-items: center;
      gap: 20px;
      position: relative;
      z-index: 2;
      width: 100%;
      
      &_wrapper {
        ${flex.col};
        gap: 4px;
        width: calc(100% - 60px);

        .user_info {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;
          
          .name {
            font-weight: ${props => props.hasUnread && '500'};
            flex: 1;
          }
          
          .time {
            font-size: ${textSizes['10']};
            color: ${colors.gray};
            white-space: nowrap;
            margin-left: 8px;
          }
        }

        .preview {
          color: ${colors.blue};
          font-size: ${textSizes['14']};
          display: flex;
          align-items: center;
          gap: 4px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          max-width: 100%;
        }
      }
    }
  }
`;