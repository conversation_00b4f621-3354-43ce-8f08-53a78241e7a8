import React from "react";
import ModalWindow from "@components/ModalWindow";
import { Button, CircularProgress, Divider, FormControl, IconButton, Typography } from "@mui/material";
import { Box } from "@mui/system";
import { Controller, useForm } from "react-hook-form";
import styled from "styled-components";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { textSizes } from "@styles/vars";
import CustomSelect from "@ui/Select";
import { bg } from "@components/Widget/style";
import Field from "@ui/Field";
import DateInput from "@components/MaskedInputs/Date";
import moment from "moment";
import TextArea from "@ui/TextArea/TextArea";
import { addDoc, collection, Timestamp } from "firebase/firestore";
import { db } from "@config/firebase.config";
import { COLLECTIONS } from "@constants/app";
import { useSnackbar } from "notistack";
import { useDispatch, useSelector } from "react-redux";
import { addNewAppointmentAction } from "@store/slices/appointments";
import { nanoid } from "nanoid";
// Removed calculateNextVisit import - now handled by cloud functions
import { useNavigate } from "react-router-dom";
import { Close } from "@mui/icons-material";
import { useEffect } from "react";
import { checkAppointmentConflict, formatConflictErrorMessage, checkCaregiverAppointmentConflict, formatCaregiverConflictErrorMessage } from "@utils/appointmentValidation";

const Label = styled.label`
  font-size: ${textSizes["14"]};
  width: fit-content;
  margin-bottom: 8px;
  display: block;
`;

const Container = styled.div`
  width: 100%;
  max-width: 700px;
  background-color: ${bg};
  padding: 20px;
  border-radius: 12px;
  // margin: 100px 0;
`;

const StyledField = styled(Field)`
  width: 100%;
`;

const TIME_OPTIONS = [
  { value: "06:00", label: "6:00 AM" },
  { value: "07:00", label: "7:00 AM" },
  { value: "08:00", label: "8:00 AM" },
  { value: "09:00", label: "9:00 AM" },
  { value: "10:00", label: "10:00 AM" },
  { value: "11:00", label: "11:00 AM" },
  { value: "12:00", label: "12:00 PM" },
  { value: "13:00", label: "1:00 PM" },
  { value: "14:00", label: "2:00 PM" },
  { value: "15:00", label: "3:00 PM" },
  { value: "16:00", label: "4:00 PM" },
  { value: "17:00", label: "5:00 PM" },
  { value: "18:00", label: "6:00 PM" },
  { value: "19:00", label: "7:00 PM" },
  { value: "20:00", label: "8:00 PM" },
  { value: "21:00", label: "9:00 PM" },
  { value: "22:00", label: "10:00 PM" },
];

const scheduleAppointmentSchema = z
  .object({
    caregiver: z.string().nonempty("Caregiver must be assigned"),
    client: z.string().optional(),
    date: z.string().nonempty("Date is required"),
    startTime: z.string().nonempty("Start time is required"),
    endTime: z.string().nonempty("End time is required"),
    comments: z.string().optional(),
    isFromCalendar: z.boolean().default(false),
  })
  .refine(
    (data) => {
      const { startTime, endTime } = data;
      if (!startTime || !endTime) return true;
      const start = moment(`${moment().format("YYYY-MM-DD")} ${startTime}`, "YYYY-MM-DD HH:mm");
      const end = moment(`${moment().format("YYYY-MM-DD")} ${endTime}`, "YYYY-MM-DD HH:mm");
      return end.isAfter(start);
    },
    {
      path: ["endTime"],
      message: "End time must be after start time",
    },
  )
  .refine(
    (data) => {
      if (data.isFromCalendar) {
        return !!data.client;
      }
      return true;
    },
    {
      path: ["client"],
      message: "Patient is required",
    },
  )
  .refine(
    (data) => {
      // Date must be within allowed range
      if (data.date) {
        const selectedDate = moment(data.date);
        const threeMonthsFromNow = moment().add(2, "months").endOf("month");
        return selectedDate.isSameOrBefore(threeMonthsFromNow);
      }
      return true;
    },
    {
      path: ["date"],
      message: "Please select a valid date",
    },
  );

const ScheduleAppointmentModal = ({
  isVisible,
  onCloseModal,
  caregiver_options,
  client,
  showCloseBtn = false,
  defaultValues,
  mode,
  client_options,
  commonNurse,
}) => {
  const { enqueueSnackbar } = useSnackbar();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const isFromCalendar = mode === "caregiver_calendar";

  const {
    handleSubmit,
    control,
    formState: { errors, isSubmitting },
    setValue,
    setError,
    watch,
    reset,
  } = useForm({
    defaultValues: {
      caregiver: "",
      client: "",
      date: "",
      startTime: "",
      endTime: "",
      comments: "",
    },
    resolver: zodResolver(scheduleAppointmentSchema),
    mode: "all",
  });
  
  async function submitForm(formValues) {
    // * Caregiver selected from calendar, create a booking
    if (isFromCalendar) {
      const { caregiver, client, date, startTime, endTime, comments } = formValues;      // Check for patient appointment conflicts
      try {
        const conflictCheck = await checkAppointmentConflict(client, date, startTime, endTime, null, null);
        if (conflictCheck.hasConflict) {
          const errorMessage = formatConflictErrorMessage(conflictCheck.conflictingAppointments);
          setError("startTime", { type: "manual", message: errorMessage });
          enqueueSnackbar("Patient appointment conflict detected. Please choose a different time.", { variant: "error" });
          return;
        }
      } catch (error) {
        enqueueSnackbar(`Error validating patient appointment time: ${error.message}`, { variant: "error" });
        return;
      }

      // Check for caregiver appointment conflicts
      try {
        const caregiverConflictCheck = await checkCaregiverAppointmentConflict(caregiver, date, startTime, endTime);
        if (caregiverConflictCheck.hasConflict) {
          const errorMessage = formatCaregiverConflictErrorMessage(caregiverConflictCheck.conflictingAppointments);
          setError("caregiver", { type: "manual", message: errorMessage });
          enqueueSnackbar("Caregiver scheduling conflict detected. Please choose a different caregiver or time.", { variant: "error" });
          return;
        }
      } catch (error) {
        enqueueSnackbar(`Error validating caregiver availability: ${error.message}`, { variant: "error" });
        return;
      }

      const payload = {
        nurse: commonNurse,
        caregiver,
        client,
        startDateTime: `${date} ${startTime}`,
        endDateTime: `${date} ${endTime}`,
        comments: comments || "",
        serviceType: "auxiliary_nurse", // default service type
        status: "SCHEDULED",
        recurrence: null, // no recurrence for single appointments
        // NEW FIELDS FOR RECURRING APPOINTMENTS
        refID: null, // null for single appointments
        isRecurringInstance: false, // false for single appointments
        originalRecurrence: null, // null for single appointments
        // VISITS OBJECT - embedded visits data for each appointment
        visits: {
          id: nanoid(), // Firebase document ID for the visit
          date: date, // visit date
          clockIn: `${date} ${startTime}`, // scheduled start time
          clockOut: `${date} ${endTime}`, // scheduled end time
          status: "scheduled", // scheduled, completed, cancelled, missed
          notes: "", // visit notes
          createdAt: Timestamp.fromDate(new Date()),
          updatedAt: Timestamp.fromDate(new Date()), // when status was last updated
        },
        createdAt: Timestamp.fromDate(new Date()),
        updatedAt: Timestamp.fromDate(new Date()),
      };

      // Use addDoc to let Firebase generate the appointment ID automatically
      await addDoc(collection(db, COLLECTIONS.APPOINTMENTS), payload)
        .then((docRef) => {
          const appointmentWithId = { id: docRef.id, ...payload };
          dispatch(addNewAppointmentAction(appointmentWithId));
          enqueueSnackbar("New appointment added", { variant: "success" });
          onCloseModal();
          reset();
        })
        .catch((error) => {
          enqueueSnackbar("Couldn't create the appointment", { variant: "error" });
        });
    }
    // * After creating client, create a booking
    else {
      const { caregiver, date, startTime, endTime, comments } = formValues;      // Check for patient appointment conflicts
      console.log('🔍 Starting conflict check for client appointment:', { clientId: client?.id, date, startTime, endTime });
      try {
        const conflictCheck = await checkAppointmentConflict(client?.id, date, startTime, endTime, null, null);
        if (conflictCheck.hasConflict) {
          const errorMessage = formatConflictErrorMessage(conflictCheck.conflictingAppointments);
          setError("startTime", { type: "manual", message: errorMessage });
          enqueueSnackbar("Patient appointment conflict detected. Please choose a different time.", { variant: "error" });
          return;
        }
      } catch (error) {
        console.error("Error checking patient appointment conflict:", error);
        enqueueSnackbar(`Error validating patient appointment time: ${error.message}`, { variant: "error" });
        return;
      }

      // Check for caregiver appointment conflicts
      console.log('🔍 Starting caregiver conflict check:', { caregiverId: caregiver, date, startTime, endTime });
      try {
        const caregiverConflictCheck = await checkCaregiverAppointmentConflict(caregiver, date, startTime, endTime);
        if (caregiverConflictCheck.hasConflict) {
          const errorMessage = formatCaregiverConflictErrorMessage(caregiverConflictCheck.conflictingAppointments);
          setError("caregiver", { type: "manual", message: errorMessage });
          enqueueSnackbar("Caregiver scheduling conflict detected. Please choose a different caregiver or time.", { variant: "error" });
          return;
        }
      } catch (error) {
        console.error("Error checking caregiver appointment conflict:", error);
        enqueueSnackbar(`Error validating caregiver availability: ${error.message}`, { variant: "error" });
        return;
      }

      const payload = {
        nurse: client?.assignedNurse,
        caregiver,
        client: client?.id,
        startDateTime: `${date} ${startTime}`,
        endDateTime: `${date} ${endTime}`,
        comments: comments || "",
        serviceType: "auxiliary_nurse", // default service type
        status: "SCHEDULED",
        recurrence: null, // no recurrence for single appointments
        // NEW FIELDS FOR RECURRING APPOINTMENTS
        refID: null, // null for single appointments
        isRecurringInstance: false, // false for single appointments
        originalRecurrence: null, // null for single appointments
        // VISITS OBJECT - embedded visits data for each appointment
        visits: {
          id: nanoid(), // Firebase document ID for the visit
          date: date, // visit date
          clockIn: `${date} ${startTime}`, // scheduled start time
          clockOut: `${date} ${endTime}`, // scheduled end time
          status: "scheduled", // scheduled, completed, cancelled, missed
          notes: "", // visit notes
          createdAt: Timestamp.fromDate(new Date()),
          updatedAt: Timestamp.fromDate(new Date()), // when status was last updated
        },
        createdAt: Timestamp.fromDate(new Date()),
        updatedAt: Timestamp.fromDate(new Date()),
      };

      // Use addDoc to let Firebase generate the appointment ID automatically
      await addDoc(collection(db, COLLECTIONS.APPOINTMENTS), payload)
        .then((docRef) => {
          const appointmentWithId = { id: docRef.id, ...payload };
          dispatch(addNewAppointmentAction(appointmentWithId));
          enqueueSnackbar("New appointment added", { variant: "success" });
          navigate("/clients");
        })
        .catch((error) => {
          enqueueSnackbar("Couldn't create the appointment", { variant: "error" });
        });
    }
  }

  useEffect(() => {
    if (isVisible && isFromCalendar) {
      setValue("isFromCalendar", isFromCalendar);
      setValue("caregiver", defaultValues?.caregiver);
      setValue("date", defaultValues?.date);
    }
  }, [isVisible]);

  return (
    <>
      <ModalWindow isVisible={isVisible} visibilityHandler={onCloseModal} key={isVisible}>
        <Box sx={{ display: "flex", justifyContent: "center", alignItems: "center", mt: { xs: 20, sm: 20, lg: 13 } }}>
          <Container className={isVisible ? "visible" : ""} top={0}>
            <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 2 }}>
              <Typography variant="h6">Schedule Appointment</Typography>
              {showCloseBtn && (
                <IconButton size="small" onClick={onCloseModal} sx={{ alignSelf: "start" }}>
                  <Close sx={{ height: 16, width: 16 }} />
                </IconButton>
              )}
            </Box>

            <form onSubmit={handleSubmit(submitForm)}>
              {/* CAREGIVER */}
              <Box position="relative" height="fit-content">
                <Controller
                  name="caregiver"
                  control={control}
                  render={({ field }) => (
                    <>
                      <Label htmlFor="caregiver">Caregiver</Label>
                      <CustomSelect
                        options={caregiver_options}
                        variant="basic"
                        value={caregiver_options.find((opt) => opt.value === field.value) || null}
                        changeHandler={(selected) => field.onChange(selected?.value)}
                        placeholder="Select Caregiver"
                        disabled={!!defaultValues?.caregiver}
                        // key={watch("caregiver")}
                      />
                      {errors?.caregiver?.message && (
                        <Typography color="error" variant="caption">
                          {errors?.caregiver?.message}
                        </Typography>
                      )}
                    </>
                  )}
                />
              </Box>

              {/* CLIENT */}
              {isFromCalendar ? (
                <Box position="relative" height="fit-content" mt={2}>
                  <Controller
                    name="client"
                    control={control}
                    render={({ field }) => (
                      <>
                        <Label htmlFor="caregiver">Patient</Label>
                        <CustomSelect
                          options={client_options}
                          variant="basic"
                          value={client_options?.find((opt) => opt.value === field.value) || null}
                          changeHandler={(selected) => field.onChange(selected?.value)}
                          placeholder="Select Patient"
                        />
                        {errors?.client?.message && (
                          <Typography color="error" variant="caption">
                            {errors?.client?.message}
                          </Typography>
                        )}
                      </>
                    )}
                  />
                </Box>
              ) : null}

              {/* DATE */}
              <Box position="relative" height="fit-content" mt={2}>
                <Controller
                  name="date"
                  control={control}
                  render={({ field }) => (
                    <>
                      <Label htmlFor="date">Date</Label>
                      <DateInput
                        id="date"
                        minDate={new Date()}
                        maxDate={moment().add(2, "months").endOf("month").toDate()}
                        onChange={(val) => {
                          const formatted_val = moment(val).format("YYYY-MM-DD");
                          setValue("date", formatted_val);
                          setError("date", { message: undefined });
                        }}
                        value={watch("date")}
                        disabled={!!defaultValues?.date}
                      />
                      {errors?.date?.message && (
                        <Typography color="error" variant="caption">
                          {errors?.date?.message}
                        </Typography>
                      )}
                    </>
                  )}
                />
              </Box>

              {/* FROM - START TIME */}
              <Box position="relative" height="fit-content" mt={2}>
                <Controller
                  name="startTime"
                  control={control}
                  render={({ field }) => {
                    return (
                      <>
                        <Label htmlFor="startTime">Start Time</Label>
                        <CustomSelect
                          label="Start Time"
                          placeholder="Select start time"
                          options={TIME_OPTIONS}
                          value={TIME_OPTIONS.find((item) => item.value === field.value)}
                          variant="basic"
                          changeHandler={(selectedOption) => field.onChange(selectedOption?.value)}
                          isSearchable={false}
                        />
                      </>
                    );
                  }}
                />
                {errors?.startTime?.message && (
                  <Typography color="error" variant="caption">
                    {errors?.startTime?.message}
                  </Typography>
                )}
              </Box>

              {/* TO- END TIME */}
              <Box position="relative" height="fit-content" mt={2}>
                <Controller
                  name="endTime"
                  control={control}
                  render={({ field }) => {
                    return (
                      <>
                        <Label htmlFor="endTime">End Time</Label>
                        <CustomSelect
                          label="End Time"
                          placeholder="Select end time"
                          options={TIME_OPTIONS}
                          value={TIME_OPTIONS.find((item) => item.value === field.value)}
                          variant="basic"
                          changeHandler={(selectedOption) => field.onChange(selectedOption?.value)}
                          isSearchable={false}
                        />
                      </>
                    );
                  }}
                />
                {errors?.endTime?.message && (
                  <Typography color="error" variant="caption">
                    {errors?.endTime?.message}
                  </Typography>
                )}
              </Box>

              {/* COMMENTS */}
              <Box position="relative" height="fit-content" mt={2}>
                <Controller
                  name="comments"
                  control={control}
                  render={({ field }) => {
                    return (
                      <>
                        <Label htmlFor="comments">Comments</Label>
                        <TextArea placeholder="Comments" id="comments" {...field} />
                      </>
                    );
                  }}
                />
              </Box>

              <FormControl
                sx={{
                  display: "flex",
                  marginTop: 2,
                  justifyContent: "end",
                  alignItems: "end",
                  gridColumn: { xs: "1", md: "1/3" },
                  gap: 1.5,
                }}
              >
                <Button
                  fullWidth
                  variant="contained"
                  color="primary"
                  type="submit"
                  sx={{
                    fontSize: 16,
                    borderRadius: 2,
                    textTransform: "none",
                    fontWeight: 400,
                  }}
                  disabled={isSubmitting}
                >
                  {"Save"}
                  {isSubmitting ? <CircularProgress size={16} color="inherit" sx={{ marginLeft: 1 }} /> : null}
                </Button>
              </FormControl>
            </form>
          </Container>
        </Box>
      </ModalWindow>
    </>
  );
};

export default ScheduleAppointmentModal;
