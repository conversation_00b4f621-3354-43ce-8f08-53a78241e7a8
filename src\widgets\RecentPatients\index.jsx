import { useRef, useEffect, useMemo } from "react";
import Widget from "@components/Widget";
import WidgetHeader from "@components/Widget/WidgetHeader";
import useContentHeight from "@hooks/useContentHeight";
import ScrollContainer from "@components/ScrollContainer";
import NoDataPlaceholder from "@components/NoDataPlaceholder";
import { Box, Typography } from "@mui/material";
import Avatar from "@ui/Avatar";
import { ReactComponent as RecentPatientArrowIcon } from "@assets/RecentPatientArrow.svg";
import WidgetBody from "@components/Widget/WidgetBody";
import { useSelector, useDispatch } from "react-redux";
import { getNameInitials } from "@utils/helpers";
import { useNavigate } from "react-router-dom";
import moment from "moment";


const RecentPatient = () => {
  const headerRef = useRef(null);
  const height = useContentHeight(headerRef);
  const dispatch = useDispatch();

  const { clients, nurses, caregivers } = useSelector((state) => state.users);
  const { appointments } = useSelector((state) => state.appointments);

  const recent_patients = clients?.slice(0, 4);

  // Get upcoming appointments directly from appointments collection
  const upcomingAppointments = appointments
    ?.filter(appointment => {
      const appointmentDate = new Date(appointment.startDateTime);
      const now = new Date();
      return appointmentDate >= now && appointment.status === 'SCHEDULED';
    })
    ?.sort((a, b) => new Date(a.startDateTime) - new Date(b.startDateTime))
    ?.slice(0, 10) // Limit to 10 upcoming appointments
    ?.map(appointment => {
      const client = clients?.find(c => c.id === appointment.client);
      return {
        id: appointment.id,
        patient: {
          name: client ? `${client.firstName} ${client.lastName}` : 'Unknown Patient',
          avatar: {
            jpg: client?.photo?.url || '',
            webp: client?.photo?.url || ''
          }
        },
        doctor: {
          name: 'Care Team',
          avatar: {
            jpg: '',
            webp: ''
          }
        },
        type: appointment.serviceType || 'Care Service',
        date: new Date(appointment.startDateTime),
        payment: 0,
        phone: client?.phone || '',
        appointment: appointment
      };
    }) || [];

  const isLoadingUpcoming = false; // No longer loading from visits

  // Create a map of patient ID to their next visit data
  const patientNextVisits = useMemo(() => {
    const visitMap = {};
    upcomingAppointments?.forEach(appointment => {
      const clientId = appointment?.appointment?.client;
      if (clientId && (!visitMap[clientId] || new Date(appointment.date) < new Date(visitMap[clientId].date))) {
        visitMap[clientId] = appointment;
      }
    });
    return visitMap;
  }, [upcomingAppointments]);

  const findNurse = (nurseId) => {
    return nurses?.find((item) => item?.id === nurseId);
  };
  const findCaregiver = (caregiverId) => {
    return caregivers?.find((item) => item?.id === caregiverId);
  };

  const getNextVisitForPatient = (patientId) => {
    return patientNextVisits[patientId] || null;
  };

  return (
    <>
      <Widget name="RecentPatients" mobile={500}>
        <WidgetHeader
          title="Recent Patients"
          style={{ paddingBottom: 19 }}
          flex="column"
          elRef={headerRef}
        ></WidgetHeader>
        <WidgetBody>
          <ScrollContainer height={height}>
            <div className="track">
              {recent_patients?.length > 0 ? (
                recent_patients?.map((item, index) => (
                  <RecentPatientItem
                    key={index}
                    patient={item}
                    nurse={findNurse(item?.assignedNurse)}
                    caregiver={findCaregiver(item?.assignedCaregiver)}
                    nextVisit={getNextVisitForPatient(item?.id)}
                    isLoadingVisits={isLoadingUpcoming}
                  
                  />
                ))
              ) : (
                <NoDataPlaceholder />
              )}
            </div>
          </ScrollContainer>
        </WidgetBody>
      </Widget>
    </>
  );
};

export default RecentPatient;

const RecentPatientItem = ({ patient, nurse, caregiver, nextVisit, isLoadingVisits }) => {
  const navigate = useNavigate();

  // Format the next appointment date and time
  const formatNextVisitInfo = () => {
    if (isLoadingVisits) {
      return {
        date: "Loading...",
        time: "Loading..."
      };
    }

    if (!nextVisit || !nextVisit.appointment) {
      return {
        time: "No schedule",
        date: "No schedule",
      };
    }

    const appointment = nextVisit.appointment;
    const appointmentDate = moment(appointment.startDateTime);
    const endTime = moment(appointment.endDateTime);

    return {
      date: appointmentDate.format("ddd, MMM DD"),
      time: appointmentDate.format("hh:mm A") + " - " + endTime.format("hh:mm A")
    };
  };

  const visitInfo = formatNextVisitInfo();

  return (
    <>
      <Box
        className="recent-patient-item"
        display="flex"
        flexDirection="column"
        alignItems="center"
        gap={"24px"}
        sx={{
          width: "100%",
          borderBottom: "1px dashed rgba(212, 212, 212, 1)",
          py: 3,
          "&.recent-patient-item:first-of-type": { paddingTop: 0 },
          "&.recent-patient-item:last-of-type": { border: "none", paddingBottom: 0 },
        }}
      >
        <Box display="flex" alignItems="flex-start" gap={"12px"} width={"100%"}>
          <Avatar
            avatar={{ jpg: patient?.photo?.url || "" }}
            initals={getNameInitials(patient?.firstName, patient?.lastName)}
          />
          <Box>
            <Typography fontWeight={500} fontSize={"18px"}>
              {patient?.name}
            </Typography>
            <Typography fontSize={"8px"} color="primary.main">
              {patient?.email}
            </Typography>
          </Box>
          <button style={{ marginLeft: "auto" }} onClick={() => navigate(`/clients/${patient?.id}`)}>
            <RecentPatientArrowIcon />
          </button>
        </Box>

        <Box display="flex" alignItems="center" width={"100%"}>
          <Typography
            fontSize={10}
            display="flex"
            whiteSpace={"wrap"}
            alignItems="center"
            gap={1}
            color="rgba(155, 161, 164, 1)"
            flex="1 1 50%"
          >
            <i className="icon icon-calendar"></i>
            <span>{visitInfo.date}</span>
          </Typography>
          <Typography
            fontSize={10}
            display="flex"
            alignItems="center"
            gap={1}
            color="rgba(155, 161, 164, 1)"
            flex="1 1 50%"
          >
            <i className="icon icon-clock"></i>
            <span>{visitInfo.time}</span>
          </Typography>
        </Box>

        <Box display="flex" alignItems="center" justifyContent="space-between" width="100%" gap={1}>
          {/* NURSE */}
          {nurse?.id ? (
            <Box flex="1 1 50%">
              <Typography fontSize={12} mb={1}>
                Assigned Nurse
              </Typography>
              <Box display="flex" alignItems="center" gap={"8px"} width={"100%"}>
                <Avatar
                  avatar={{ jpg: nurse?.photo?.url || "" }}
                  initals={getNameInitials(nurse?.firstName, nurse?.lastName)}
                  size={28}
                />
                <Box>
                  <Typography fontWeight={500} fontSize={"12px"}>
                    {nurse?.name}
                  </Typography>
                  <Typography fontSize={"10px"} color="primary.main">
                    {nurse?.email}
                  </Typography>
                </Box>
              </Box>
            </Box>
          ) : null}

          {/* CAREGIVER */}
          {caregiver?.id ? (
            <Box flex="1 1 50%">
              <Typography fontSize={12} mb={1}>
                Assigned Caregiver
              </Typography>
              <Box display="flex" alignItems="center" gap={"12px"} width={"100%"}>
                <Avatar
                  avatar={{ jpg: caregiver?.photo?.url || "" }}
                  initals={getNameInitials(caregiver?.firstName, caregiver?.lastName)}
                  size={28}
                />
                <Box>
                  <Typography fontWeight={500} fontSize={"12px"}>
                    {caregiver?.name}
                  </Typography>
                  <Typography fontSize={"10px"} color="primary.main">
                    {caregiver?.email}
                  </Typography>
                </Box>
              </Box>
            </Box>
          ) : null}
        </Box>
      </Box>
    </>
  );
};
