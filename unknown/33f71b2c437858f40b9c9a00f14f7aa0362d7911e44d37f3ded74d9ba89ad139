import { useState } from "react";
import NoDataPlaceholder from "@components/NoDataPlaceholder";
import Page from "@layout/Page";
import { Box, Grid, Typography, IconButton } from "@mui/material";
import { colors, effects, flex } from "@styles/vars";
import Avatar from "@ui/Avatar";
import { getNameInitials } from "@utils/helpers";
import React from "react";
import { useSelector } from "react-redux";
import { useParams, useNavigate } from "react-router-dom";
import styled from "styled-components";
import { ReactComponent as PatientDocumentIcon } from "@assets/PatientDocument.svg";
import { ReactComponent as IDcardIcon } from "@assets/IDcard.svg";
import { ReactComponent as PhoneIcon } from "@assets/Phone.svg";
import { ReactComponent as AppointmentsHeadingIcon } from "@assets/AppointmentsHeading.svg";
import moment from "moment";
import { bg, borderShadow } from "@components/Widget/style";
import AppointmentListItem from "@components/AppointmentListItem/AppointmentListItem";
import AppointmentStatus from "@components/AppointmentListItem/AppointmentStatus";
import { Block, Wrapper } from "@components/AppointmentListItem/style";
import { Tab } from "react-bootstrap";
import TabNavItem from "@ui/TabNav/TabNavItem";
import { theme as muiTheme } from "@styles/mui-theme";
import theme from "styled-theming";
import { Nav } from "react-bootstrap";
import { CAREGIVER_TYPE_OPTIONS } from "@constants/app";
import { Visibility } from "@mui/icons-material";

const Card = styled.div`
  box-shadow: ${effects.widgetShadow};
  border-radius: 16px;
  position: relative;
  background-color: ${bg};
  overflow: hidden;
  ${flex.col};
  // min-height: 182px;
  // flex-grow: 1;
  // ${(props) => props.mobile && `height: ${props.mobile}px`};
  // iOS fix
  transform: translate3d(0, 0, 0);

  &.shadow {
    &:before,
    &:after {
      display: block;
    }
  }

  &:before,
  &:after {
    content: "";
    position: absolute;
    top: 0;
    background: ${borderShadow};
    height: 100%;
    width: 24px;
    z-index: 3;
    filter: blur(1px);
    display: none;
  }

  &:before {
    left: -2px;
    transform: ${(props) => (props.dir === "rtl" ? "scaleX(-1)" : "scaleX(1)")};
  }

  &:after {
    right: -2px;
    transform: rotate(180deg) ${(props) => (props.dir === "rtl" ? "scaleX(-1)" : "scaleX(1)")};
  }
`;

const pillBgColor = theme("theme", {
  light: "#E0E7FF",
  dark: "#4C1D95",
});

const PillText = styled(Typography)`
  width: fit-content;
  background-color: ${pillBgColor};
  font-size: 14px !important;
  padding: 4px 18px;
  border-radius: 10px;
  margin-top: 6px !important;
`;

const DocPreview = styled.img`
  height: 40px;
  width: 60px;
  border-radius: 4px;
`;

const IDPreview = styled.img`
  height: 100px;
  border-radius: 8px;
`;

const TabNavContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  border-radius: 6px;
  overflow: hidden;
  margin-top: 32px;

  .nav-item {
    flex: 1 1 auto; /* Default: desktop, 4 items per row */
  }

  /* Tablet: 2 items per row */
  @media screen and (max-width: 590px) {
    .nav-item {
      flex: 0 0 50%;
      max-width: 50%;
    }
  }

  /* Mobile: 1 item per row */
  @media screen and (max-width: 360px) {
    .nav-item {
      flex: 0 0 100%;
      max-width: 100%;
    }
  }
`;

const StyledTabNav = ({ children }) => {
  return <TabNavContainer as={Nav}>{children}</TabNavContainer>;
};

const StaffDetails = () => {
  const { staffId } = useParams();
  const [activeTab, setActiveTab] = useState("PROFILE");

  const { user: logged_user } = useSelector((state) => state.auth);
  const { caregivers, nurses } = useSelector((state) => state.users);
  const { appointments } = useSelector((state) => state.appointments);

  const staff = [...caregivers, ...nurses]?.find((staff) => staff.id === staffId);
  const caregiver = caregivers?.find((item) => item?.id === staff?.assignedCaregiver);
  const nurse = nurses?.find((item) => item?.id === staff?.assignedNurse);
  const client_appointments = appointments?.filter((item) => item?.staff === staffId);
  const findCaregiver = (caregiverId) => caregivers?.find((item) => item?.id === caregiverId);

  const findNurse = () => {
    if (logged_user?.role === "NURSE") {
      return logged_user;
    } else if (logged_user?.role === "ADMIN") {
      return nurses?.find((item) => item?.id === staff?.assignedNurse);
    }
  };

  if (!staff) {
    return (
      <Page title="Staff Details">
        <NoDataPlaceholder />
      </Page>
    );
  }

  return (
    <>
      <Page title="Staff Details">
        {/* STAFF DETAILS CARD */}
        <Card name="StaffDetails">
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              padding: { xs: "16px", sm: "36px" },
              flexDirection: { xs: "column", sm: "row" },
              gap: { xs: 2, lg: 8 },
            }}
          >
            <Box
              sx={{
                display: "flex",
                justifyContent: { xs: "center", sm: "flex-start" },
                alignItems: { xs: "center", sm: "flex-start" },
                flexDirection: { xs: "column", sm: "row" },
                gap: { xs: ".5rem", sm: "2rem" },
                width: "100%",
              }}
            >
              <Avatar
                alt={staff?.name}
                avatar={{ jpg: staff?.photo?.url }}
                initals={getNameInitials(staff?.firstName, staff?.lastName)}
                size={90}
              />
              <div>
                <Typography
                  variant="h6"
                  fontWeight={500}
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    gap: "1px",
                    justifyContent: "center",
                    alignItems: {
                      xs: "center",
                      sm: "flex-start",
                      md: "flex-start",
                      lg: "flex-start",
                      xl: "flex-start",
                    },
                  }}
                >
                  {staff?.name}
                </Typography>

                <Typography
                  variant="body2"
                  // fontSize="12px"
                >
                  {staff?.email}
                </Typography>
                {/* <Typography
                  variant="body1"
                  fontWeight={400}
                  fontSize="12px"
                  sx={{
                    backgroundColor: staff?.isActive ? "#00898c1c" : "#FEE2E2",
                    color: staff?.isActive ? "#00898c" : "#B91C1C",
                    padding: "4px 12px",
                    borderRadius: "99px",
                    width: "fit-content",
                  }}
                  mt={1}
                >
                  {staff?.isActive ? "Active" : "Inactive"}
                </Typography> */}
              </div>
            </Box>
          </Box>
        </Card>

        {/* TABS */}
        <Tab.Container defaultActiveKey="PROFILE" transition={true} activeKey={activeTab} onSelect={setActiveTab}>
          <StyledTabNav>
            <TabNavItem eventKey="PROFILE" title="Profile" handler={() => setActiveTab("PROFILE")} />
            <TabNavItem eventKey="APPOINTMENTS" title="Appointments" handler={() => setActiveTab("APPOINTMENTS")} />
          </StyledTabNav>

          <Tab.Content>
            <Tab.Pane active={activeTab === "PROFILE"} eventKey="PROFILE">
              <ProfileTabContent staff={staff} nurse={findNurse()} />
            </Tab.Pane>
            <Tab.Pane active={activeTab === "APPOINTMENTS"} eventKey="APPOINTMENTS">
              <AppointmentsTabContent staff={staff} />
            </Tab.Pane>
          </Tab.Content>
        </Tab.Container>
      </Page>
    </>
  );
};

// Component for displaying appointments from staff perspective (showing patient info)
const StaffAppointmentListItem = ({ appointment, patient }) => {
  const navigate = useNavigate();

  const PatientBlock = () => {
    // Generate firstName and lastName from the single name field for getNameInitials
    const nameParts = patient?.name?.split(" ") || [];
    const firstName = nameParts[0] || "";
    const lastName = nameParts.slice(1).join(" ") || "";

    return (
      <Block>
        <Avatar
          avatar={{ jpg: patient?.photo?.url }}
          alt={patient?.name}
          initals={getNameInitials(patient?.firstName, patient?.lastName)}
        />
        <div className="main">
          <span className="name">{patient?.name}</span>
          <span className="age">{`Patient`}</span>
        </div>
      </Block>
    );
  };

  return (
    <>
      <Wrapper>
        <PatientBlock />
        <Box sx={{ mr: 2 }}>
          <Typography variant="body2" fontWeight={600}>
            Date
          </Typography>
          <Typography variant="body2">{moment(appointment?.startDateTime).format("MMMM DD, YYYY")}</Typography>
        </Box>
        <Box sx={{ mr: 2 }}>
          <Typography variant="body2" fontWeight={600}>
            Time
          </Typography>
          <Typography variant="body2">
            {moment(appointment?.startDateTime).format("hh:mm A")} -{" "}
            {moment(appointment?.endDateTime).format("hh:mm A")}
          </Typography>
        </Box>
        <AppointmentStatus status={appointment?.status} />
        <Block className="actions">
          <IconButton
            sx={{ ":hover svg": { fill: "#fff" } }}
            onClick={() => navigate(`/appointments/${appointment?.id}`)}
          >
            <Visibility />
          </IconButton>
        </Block>
      </Wrapper>
    </>
  );
};

export default StaffDetails;

const ProfileTabContent = ({ staff }) => {
  return (
    <>
      <Grid container spacing={2} mt={4}>
        {/* LEFT SIDE */}
        <Grid size={{ xs: 12, sm: 6 }} display="flex" flexDirection="column" gap={2}>
          <Card>
            <Box px={3} py={2}>
              <Grid container rowGap={2}>
                <Grid size={6}>
                  <Typography fontSize={14} color="#6B7280">
                    Gender
                  </Typography>
                  <Typography textTransform="capitalize" fontWeight={500}>
                    {staff?.gender}
                  </Typography>
                </Grid>
                <Grid size={6}>
                  <Typography fontSize={14} color="#6B7280">
                    Date of Birth
                  </Typography>
                  <Typography fontWeight={500}>{moment(staff?.dob).format("MMMM DD, YYYY")}</Typography>
                </Grid>

                <Grid size={12}>
                  <Typography fontSize={14} color="#6B7280">
                    Address
                  </Typography>
                  <Typography fontWeight={500}>{staff?.address?.formattedAddress}</Typography>
                </Grid>

                {staff?.role === "CAREGIVER" ? (
                  <Grid size={6}>
                    <Typography fontSize={14} color="#6B7280">
                      Type
                    </Typography>
                    <Typography fontWeight={500}>
                      {CAREGIVER_TYPE_OPTIONS?.find((item) => item.value === staff?.type)?.label}
                    </Typography>
                  </Grid>
                ) : null}
              </Grid>
            </Box>
          </Card>

          {/* ID */}
          <Card>
            <Box p={3} display="flex" flexDirection="column" gap={1}>
              <Box display="flex" gap={2} alignItems="center">
                <IDcardIcon fill={muiTheme.palette.primary.main} />
                <Typography fontSize={18} fontWeight={600}>
                  ID
                </Typography>
              </Box>
              <Grid container spacing={2}>
                <Grid size={6}>
                  <IDPreview src={staff?.ID?.front?.url} />
                </Grid>
                <Grid size={6}>
                  <IDPreview src={staff?.ID?.back?.url} />
                </Grid>
              </Grid>
            </Box>
          </Card>
        </Grid>
        {/* RIGHT SIDE */}
        <Grid size={{ xs: 12, sm: 6 }} display="flex" flexDirection="column" gap={2}>
          {/* EMERGENCY CONTACT PERSON */}
          <Card>
            <Box p={3} display="flex" flexDirection="column" gap={1}>
              <Box display="flex" gap={2} alignItems="center">
                <PhoneIcon fill={muiTheme.palette.primary.main} />
                <Typography fontSize={18} fontWeight={600}>
                  Emergency Contact Person
                </Typography>
              </Box>
              <Grid container rowGap={2}>
                <Grid size={6}>
                  <Typography fontSize={14} color="#6B7280">
                    Name
                  </Typography>
                  <Typography textTransform="capitalize" fontWeight={500}>
                    {staff?.emergencyContactPerson?.name || "Not provided"}
                  </Typography>
                </Grid>
                <Grid size={6}>
                  <Typography fontSize={14} color="#6B7280">
                    Phone
                  </Typography>
                  <Typography textTransform="capitalize" fontWeight={500}>
                    {staff?.emergencyContactPerson?.phone || "Not provided"}
                  </Typography>
                </Grid>
              </Grid>
            </Box>
          </Card>

          {/* DOCUMENTS*/}
          <Card>
            <Box p={3} display="flex" flexDirection="column" gap={1}>
              <Box display="flex" gap={2} alignItems="center">
                <PatientDocumentIcon fill={muiTheme.palette.primary.main} />
                <Typography fontSize={18} fontWeight={600}>
                  Documents
                </Typography>
              </Box>
              <Grid container rowGap={2}>
                <Grid size={{ xs: 12, sm: 6 }}>
                  <Typography fontSize={14} color="#6B7280">
                    License
                  </Typography>
                  <a href={staff?.license?.url} target="_blank" rel="noopener noreferrer">
                    <PillText textTransform="capitalize" fontWeight={500}>
                      {"View License"}
                    </PillText>
                  </a>
                </Grid>
                <Grid size={{ xs: 12, sm: 6 }}>
                  <Typography fontSize={14} color="#6B7280">
                    Health Certificate
                  </Typography>
                  <a href={staff?.healthCertificate?.url} target="_blank" rel="noopener noreferrer">
                    <PillText textTransform="capitalize" fontWeight={500}>
                      {"View Health Certificate"}
                    </PillText>
                  </a>
                </Grid>
                <Grid size={12}>
                  <Typography fontSize={14} color="#6B7280">
                    Certificates
                  </Typography>
                  <Box display="flex" flexWrap="wrap" gap={1}>
                    {staff?.certificates?.length
                      ? staff?.certificates?.map((item, index) => (
                          <a href={item?.url} target="_blank" rel="noopener noreferrer">
                            <PillText key={index}>{`View Certificate ${index + 1}`}</PillText>
                          </a>
                        ))
                      : null}
                  </Box>
                </Grid>
              </Grid>
            </Box>
          </Card>
        </Grid>
      </Grid>
    </>
  );
};

const AppointmentsTabContent = ({ staff }) => {
  const { staffId } = useParams();
  const { user: logged_user } = useSelector((state) => state.auth);
  const { clients, caregivers, nurses } = useSelector((state) => state.users);
  const { appointments } = useSelector((state) => state.appointments);

  // Filter appointments by staff member (either caregiver or nurse)
  const staff_appointments = appointments?.filter((item) => {
    const matchesCaregiver = item?.caregiver === staffId;
    const matchesNurse = item?.nurse === staffId;
    const matches = matchesCaregiver || matchesNurse;

    return matches;
  });

  // Sort appointments by startTimeStamp (earliest first)
  const sortedStaffAppointments = staff_appointments?.sort((a, b) => {
    const timestampA = a?.startTimeStamp || 0;
    const timestampB = b?.startTimeStamp || 0;
    
    // Sort in ascending order (earliest dates first)
    return timestampA - timestampB;
  });

  const findClient = (clientId) => {
    const client = clients?.find((item) => item?.id === clientId);
    return client;
  };

  return (
    <>
      <Box my={4}>
        <Typography variant="h5" fontWeight={600} fontSize="20px" display="flex" alignItems="center" gap={1} mb={3}>
          <AppointmentsHeadingIcon fill={colors.primary} />
          <span>Appointments</span>
        </Typography>

        <Card name="StaffAppointments">
          {staff_appointments?.length ? (
            <Box p={2} display={"flex"} flexDirection="column" gap={2}>
              {staff_appointments?.map((item) => {
                const patient = findClient(item?.client);
                return <StaffAppointmentListItem key={item?.id} appointment={item} patient={patient} />;
              })}
            </Box>
          ) : (
            <>
              <NoDataPlaceholder />
            </>
          )}
        </Card>
      </Box>
    </>
  );
};
