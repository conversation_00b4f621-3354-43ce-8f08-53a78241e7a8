import React from "react";
import { Block, Wrapper } from "./style";
import Avatar from "@ui/Avatar";
import Reminder from "@ui/Reminder";
import ActionButton from "@ui/ActionButton";
import ShapeButton from "@ui/ShapeButton";
import { getNameInitials } from "@utils/helpers";
import styled from "styled-components";
import { colors, dark, flex, fonts, light, textSizes } from "@styles/vars";
import theme from "styled-theming";
import AppointmentChip from "./AppointmentChip";
import AppointmentStatus from "./AppointmentStatus";
import Btn from "@ui/Btn";
// import { ReactComponent as EyeIcon } from "@assets/Eye.svg";
import { Box, IconButton, Typography } from "@mui/material";
import { Visibility } from "@mui/icons-material";
import moment from "moment";
import { useNavigate } from "react-router-dom";

const AppointmentListItem = ({ appointment, caregiver }) => {
  const navigate = useNavigate();

  const Common = () => {
    return (
      <Block>
        <Avatar
          avatar={{ jpg: caregiver?.photo?.url }}
          alt={`${caregiver?.firstName} ${caregiver?.lastName}`}
          initals={getNameInitials(caregiver?.firstName, caregiver?.lastName)}
        />
        <div className="main">
          <span className="name">{`${caregiver?.firstName} ${caregiver?.lastName}`}</span>
          <span className="age">{`Caregiver`}</span>
        </div>
      </Block>
    );
  };

  return (
    <>
      <Wrapper>
        <Common />
        <Box sx={{ mr: 2 }}>
          <Typography variant="body2" fontWeight={600}>
            Date
          </Typography>
          <Typography variant="body2">{moment(appointment?.startDateTime).format("MMMM DD, YYYY")}</Typography>
        </Box>
        <Box sx={{ mr: 2 }}>
          <Typography variant="body2" fontWeight={600}>
            Time
          </Typography>
          <Typography variant="body2">
            {moment(appointment?.startDateTime).format("hh:mm A")} -{" "}
            {moment(appointment?.endDateTime).format("hh:mm A")}
          </Typography>
        </Box>
        <AppointmentStatus  status={appointment?.status} />
        <Block className="actions">
          <IconButton
            sx={{ ":hover svg": { fill: "#fff" } }}
            onClick={() => navigate(`/appointments/${appointment?.id}`)}
          >
            <Visibility />
          </IconButton>
        </Block>
      </Wrapper>
    </>
  );
};

export default AppointmentListItem;
