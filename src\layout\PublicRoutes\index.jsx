import WidgetsLoader from "@components/WidgetsLoader";
import { Box } from "@mui/material";
import React from "react";
import { Suspense } from "react";
import { Navigate, Outlet } from "react-router-dom";

const PublicRoutes = () => {
  const userId = localStorage.getItem("userId");
  if (userId) {
    return <Navigate to="/dashboard" />;
  } else {
    return (
      <Suspense
        fallback={
          <Box sx={{ height: "100vh", width: "100wv", display: "grid", placeItems: "center" }}>
            <WidgetsLoader />
          </Box>
        }
      >
        <Outlet />
      </Suspense>
    );
  }
};

export default PublicRoutes;
