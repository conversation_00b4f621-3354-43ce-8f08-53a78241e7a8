// utils
import { lazy } from "react";

// components
import { Navigate, Route, Routes } from "react-router-dom";

// hooks
import { useEffect } from "react";
import { auth, messaging } from "config/firebase.config";
import { onAuthStateChanged } from "firebase/auth";
import ProtectedRoutes from "@layout/ProtectedRoutes";
import PublicRoutes from "@layout/PublicRoutes";
import { useDispatch, useSelector } from "react-redux";
import { getLoggedInUser } from "@store/slices/auth";
import { getAllCaregiversOfNurse, getAllPatientsOfNurse, getAllUsers } from "@store/slices/users";
import { getAllChats, setupChatsRealtime, cleanupChatListeners } from "@store/slices/chats";
import { getAllAppointments, getAllAppointmentsOfNurse } from "@store/slices/appointments";
import { getAllTasks } from "@store/slices/tasks";
import { getNotificationsOfAdmin, getNotificationsOfNurse } from "@store/slices/notifications";
import { onMessage } from "firebase/messaging";
import { subscribeToNotifications, cleanupSubscriptions } from "@store/slices/notifications";

// pages
const Dashboard = lazy(() => import("@pages/Dashboard"));
const DashboardA = lazy(() => import("@pages/DashboardA"));
const DashboardB = lazy(() => import("@pages/DashboardB"));
const DashboardC = lazy(() => import("@pages/DashboardC"));
const DashboardD = lazy(() => import("@pages/DashboardD"));
const DashboardE = lazy(() => import("@pages/DashboardE"));
const DashboardF = lazy(() => import("@pages/DashboardF"));
const DashboardG = lazy(() => import("@pages/DashboardG"));
const DashboardH = lazy(() => import("@pages/DashboardH"));
const DashboardI = lazy(() => import("@pages/DashboardI"));
const DashboardJ = lazy(() => import("@pages/DashboardJ"));
const DashboardK = lazy(() => import("@pages/DashboardK"));
const DoctorAppointments = lazy(() => import("@pages/DoctorAppointments"));
const Appointments = lazy(() => import("@pages/Appointments"));
const AppointmentDetails = lazy(() => import("@pages/AppointmentDetails"));
const Patients = lazy(() => import("@pages/Patients"));
const PatientDetails = lazy(() => import("@pages/PatientDetails"));
const StaffDetails = lazy(() => import("@pages/StaffDetails"));
const AddPatient = lazy(() => import("@pages/AddPatient"));
const Tests = lazy(() => import("@pages/Tests"));
const Staff = lazy(() => import("@pages/Staff"));
const StaffMessenger = lazy(() => import("@pages/DoctorMessenger"));
const PatientMessenger = lazy(() => import("@pages/PatientMessenger"));
const DoctorsReviews = lazy(() => import("@pages/DoctorsReviews"));
const PatientReviews = lazy(() => import("@pages/PatientReviews"));
const Finances = lazy(() => import("@pages/Finances"));
const Settings = lazy(() => import("@pages/Settings"));
const PageNotFound = lazy(() => import("@pages/PageNotFound"));
const Login = lazy(() => import("@pages/Login"));
const Nurses = lazy(() => import("@pages/Nurses"));
const AddUser = lazy(() => import("@pages/AddUser"));
const AddCaregiver = lazy(() => import("@pages/AddCaregiver"));
const Caregivers = lazy(() => import("@pages/Caregivers"));
const Tasks = lazy(() => import("@pages/Tasks"));
const Notifications = lazy(() => import("@pages/Notifications"));

const AppLayout = () => {
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.auth);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (currentUser) => {
      if (currentUser) {
        dispatch(getLoggedInUser(currentUser.uid));
      } else if (!currentUser) {
        console.log("current.user >", currentUser);
      }
    });

    return () => unsubscribe();
  }, []);

  useEffect(() => {
    if (user) {      // Set up real-time notification subscription for all users
      dispatch(subscribeToNotifications({ userRole: user.role, userId: user.id }));

      if (user?.role === "ADMIN") {
        dispatch(getAllUsers());
        dispatch(getAllAppointments());
        dispatch(getAllTasks());
      }
      if (user?.role === "NURSE") {
        dispatch(getAllChats(user?.id));
        dispatch(getAllCaregiversOfNurse());
        dispatch(getAllPatientsOfNurse(user?.id));
        dispatch(getAllAppointmentsOfNurse(user?.id));
        dispatch(getAllTasks());
      }
    }

    // Cleanup function
    return () => {
      dispatch(cleanupSubscriptions());
    };
  }, [user]);

  useEffect(() => {
    if (user?.id) {
      // Setup real-time listeners for chats
      dispatch(setupChatsRealtime(user.id));
      
      // Cleanup function
      return () => {
        dispatch(cleanupChatListeners());
      };
    }
  }, [user?.id, dispatch]);

  return (
    <>
      <Routes>
        {/* PUBLIC ROUTES */}
        <Route element={<PublicRoutes />}>
          <Route path="/" element={<Navigate to="/login" />} />
          <Route path="/login" element={<Login />} />
        </Route>

        {/* PROTECTED ROUTES */}
        <Route element={<ProtectedRoutes />}>
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/nurses" element={<Nurses />} />
          <Route path="/staff" element={<Staff />} />
          <Route path="/caregivers" element={<Caregivers />} />
          <Route path="/add_staff" element={<AddUser />} />
          <Route path="/add_client" element={<AddPatient />} />
          <Route path="/settings" element={<Settings />} />
          <Route path="/clients" element={<Patients />} />
          <Route path="/clients/:clientId" element={<PatientDetails />} />
          <Route path="/staff/:staffId" element={<StaffDetails />} />
          <Route path="/appointments" element={<Appointments />} />
          <Route path="/appointments/:appointmentId" element={<AppointmentDetails />} />
          <Route path="/staff_messenger" element={<StaffMessenger />} />
          <Route path="/client_messenger" element={<PatientMessenger />} />
          <Route path="/nurse_chats" element={<PatientMessenger />} />
          <Route path="/tasks" element={<Tasks />} />
          <Route path="/notifications" element={<Notifications />} />
        </Route>
      </Routes>
    </>
  );
};

export default AppLayout;
