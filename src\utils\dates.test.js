import moment from 'moment';
import { canCompleteAppointment, getAppointmentCompletionTooltip } from './dates';

// Mock moment to control "today" for testing
const mockToday = (dateString) => {
  const originalMoment = moment;
  moment.mockImplementation = jest.fn(() => originalMoment(dateString));
  return originalMoment;
};

describe('Appointment Completion Date Validation', () => {
  beforeEach(() => {
    // Reset moment to current implementation
    jest.clearAllMocks();
  });

  describe('canCompleteAppointment', () => {
    test('should return true for today\'s appointment', () => {
      const today = moment().format('YYYY-MM-DD');
      const appointmentDateTime = `${today} 14:30`;
      
      expect(canCompleteAppointment(appointmentDateTime)).toBe(true);
    });

    test('should return true for past appointment', () => {
      const yesterday = moment().subtract(1, 'day').format('YYYY-MM-DD');
      const appointmentDateTime = `${yesterday} 14:30`;
      
      expect(canCompleteAppointment(appointmentDateTime)).toBe(true);
    });

    test('should return false for future appointment', () => {
      const tomorrow = moment().add(1, 'day').format('YYYY-MM-DD');
      const appointmentDateTime = `${tomorrow} 14:30`;
      
      expect(canCompleteAppointment(appointmentDateTime)).toBe(false);
    });

    test('should return false for invalid date', () => {
      expect(canCompleteAppointment(null)).toBe(false);
      expect(canCompleteAppointment(undefined)).toBe(false);
      expect(canCompleteAppointment('')).toBe(false);
      expect(canCompleteAppointment('invalid-date')).toBe(false);
    });
  });

  describe('getAppointmentCompletionTooltip', () => {
    test('should return completion message for today\'s appointment', () => {
      const today = moment().format('YYYY-MM-DD');
      const appointmentDateTime = `${today} 14:30`;
      
      const tooltip = getAppointmentCompletionTooltip(appointmentDateTime);
      expect(tooltip).toBe('Mark this appointment as completed');
    });

    test('should return completion message for past appointment', () => {
      const yesterday = moment().subtract(1, 'day').format('YYYY-MM-DD');
      const appointmentDateTime = `${yesterday} 14:30`;
      
      const tooltip = getAppointmentCompletionTooltip(appointmentDateTime);
      expect(tooltip).toBe('Mark this appointment as completed');
    });

    test('should return restriction message for future appointment', () => {
      const tomorrow = moment().add(1, 'day').format('YYYY-MM-DD');
      const appointmentDateTime = `${tomorrow} 14:30`;
      
      const tooltip = getAppointmentCompletionTooltip(appointmentDateTime);
      const expectedDate = moment(appointmentDateTime, 'YYYY-MM-DD HH:mm').format('MMMM DD, YYYY');
      expect(tooltip).toBe(`This appointment is scheduled for ${expectedDate}. Only today's appointments can be marked as completed.`);
    });

    test('should return error message for invalid date', () => {
      expect(getAppointmentCompletionTooltip(null)).toBe('Invalid appointment date');
      expect(getAppointmentCompletionTooltip(undefined)).toBe('Invalid appointment date');
      expect(getAppointmentCompletionTooltip('')).toBe('Invalid appointment date');
    });
  });

  describe('Edge cases', () => {
    test('should handle appointments at midnight correctly', () => {
      const today = moment().format('YYYY-MM-DD');
      const midnightAppointment = `${today} 00:00`;
      
      expect(canCompleteAppointment(midnightAppointment)).toBe(true);
    });

    test('should handle appointments at end of day correctly', () => {
      const today = moment().format('YYYY-MM-DD');
      const endOfDayAppointment = `${today} 23:59`;
      
      expect(canCompleteAppointment(endOfDayAppointment)).toBe(true);
    });

    test('should handle different time formats gracefully', () => {
      const today = moment().format('YYYY-MM-DD');
      
      // Test with single digit hours/minutes
      expect(canCompleteAppointment(`${today} 9:05`)).toBe(true);
      expect(canCompleteAppointment(`${today} 09:5`)).toBe(true);
    });
  });
});

// Example usage demonstration
console.log('=== Appointment Completion Validation Examples ===');

const examples = [
  { label: 'Today 2:30 PM', date: `${moment().format('YYYY-MM-DD')} 14:30` },
  { label: 'Yesterday 10:00 AM', date: `${moment().subtract(1, 'day').format('YYYY-MM-DD')} 10:00` },
  { label: 'Tomorrow 3:00 PM', date: `${moment().add(1, 'day').format('YYYY-MM-DD')} 15:00` },
  { label: 'Next week', date: `${moment().add(7, 'days').format('YYYY-MM-DD')} 11:00` },
];

examples.forEach(example => {
  const canComplete = canCompleteAppointment(example.date);
  const tooltip = getAppointmentCompletionTooltip(example.date);
  
  console.log(`\n${example.label}:`);
  console.log(`  Can complete: ${canComplete}`);
  console.log(`  Tooltip: ${tooltip}`);
});
