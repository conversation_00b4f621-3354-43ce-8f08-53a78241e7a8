import { createSelector } from '@reduxjs/toolkit';

export const selectUnreadMessagesCount = createSelector(
  [(state) => state.chats?.chats, (state) => state.auth?.user?.id],
  (chats, userId) => {
    if (!chats || !userId) return 0;
    
    return chats.reduce((count, chat) => {
      const lastMessage = chat.lastMessage;
      if (lastMessage?.read === false && lastMessage?.senderId !== userId) {
        return count + 1;
      }
      return count;
    }, 0);
  }
);