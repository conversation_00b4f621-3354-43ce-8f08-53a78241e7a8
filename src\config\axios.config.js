import axios from "axios";

const AppInstance = axios.create({
  baseURL: "https://us-central1-insyt-care.cloudfunctions.net",
});

AppInstance.interceptors.request.use((request) => {
  const accessToken = localStorage.getItem("access_token");
  if (accessToken) {
    request.headers.Authorization = `Bearer ${accessToken}`;
  }
  return request;
});

AppInstance.interceptors.response.use((response) => response);

export default AppInstance;
