// styled components
import { ListItem, Main, Footer } from "@components/AppointmentItem/style";

// components
import Avatar from "@ui/Avatar";
import Truncated from "@components/Truncated";
import ShapeButton from "@ui/ShapeButton";
import MenuDots from "@ui/MenuDots";
import { motion, AnimatePresence } from "framer-motion";
import { Fragment } from "react";

// utils
import PropTypes from "prop-types";
import moment from "moment";
import { getNameInitials } from "@utils/helpers";

// hooks
import { useNavigate } from "react-router-dom";
import { CAREGIVER_TYPE_OPTIONS } from "@constants/app";

const AppointmentItem = ({ variant = "doctor", data, animated = false, patient }) => {
  const { patient: dataPatient, doctor, type, date, appointment } = data;
  const navigate = useNavigate();

  const WrapperElement = animated ? AnimatePresence : Fragment;

  // Handle click to navigate to appointment details
  const handleClick = () => {
    if (appointment?.id) {
      navigate(`/appointments/${appointment.id}`);
    } else if (data?.id) {
      // Fallback for cases where appointment object is not nested
      navigate(`/appointments/${data.id}`);
    }
  };

  const animationConfig = () => {
    if (animated) {
      return {
        as: motion.div,
        initial: { opacity: 0, y: 20 },
        animate: { opacity: 1, y: 0 },
        exit: { opacity: 0, y: 20 },
        transition: { duration: 0.5 },
      };
    } else return {};
  };

  const footerContent = () => {
    switch (variant) {
      default:
      case "doctor":
        return (
          <>
            <span className="details_item">
              <i className="icon icon-clock" />
              <span>{moment(date).format("hh:mm A")}</span>
            </span>

            <span className="details_item">
              <i className="icon icon-calendar" />
              <span>{moment(date).format("ddd, MMM DD")}</span>
            </span>
          </>
        );
      case "patient":
        return (
          <span className="details_item">
            <i className="icon icon-clock" />
            <span>{moment(date).format("dddd, MMMM DD")}</span>
          </span>
        );
    }
  };

  // Check if navigation is available
  const isNavigable = appointment?.id || data?.id;

  return (
    <WrapperElement>
      <ListItem
        variant={variant}
        {...animationConfig()}
        onClick={isNavigable ? handleClick : undefined}
        style={{ cursor: isNavigable ? 'pointer' : 'default' }}
      >
        <Main>
          <Avatar
            avatar={{jpg: dataPatient?.avatar?.jpg || ""}}
            initals={getNameInitials(patient?.firstName, patient?.lastName)}
          />
          <div className="info">
            <Truncated className="name" text={dataPatient?.name || patient?.name} />
            <Truncated className="title" text={CAREGIVER_TYPE_OPTIONS.find(item => item.value === type)?.label} />
          </div>
          {/* {variant !== "patient" && <ShapeButton shape="round" label="Call" icon="phone" />} */}
        </Main>
        <Footer variant={variant}>
          <div className="details">{footerContent()}</div>
          <MenuDots />
        </Footer>
      </ListItem>
    </WrapperElement>
  );
};

AppointmentItem.propTypes = {
  variant: PropTypes.oneOf(["doctor", "patient"]),
  data: PropTypes.object.isRequired,
  animated: PropTypes.bool,
};

export default AppointmentItem;
