// styling
import styled from "styled-components/macro";

// components
import { Badge } from "@ui/Badge/style";

// assets
import placeholder from "@assets/placeholder.jpg";
import empty_avatar from "@assets/avatars/empty_avatar.png";

// utils
import PropTypes from "prop-types";

const Container = styled.div`
  width: ${(props) => `${props.size}px`};
  height: ${(props) => `${props.size}px`};
  aspect-ratio: 1 / 1;
  position: relative;

  .indicator {
    position: absolute;
    right: -6px;
    top: -6px;
    z-index: 2;
  }
`;
const Img = styled.picture`
  img {
    border-radius: ${(props) => (props.isRounded ? "50%" : "8px")};
    overflow: hidden;
    position: relative;
    z-index: 1;
  }
`;

const Initals = styled.p`
  width: ${(props) => `${props.size}px`};
  height: ${(props) => `${props.size}px`};
  aspect-ratio: 1 / 1;
  position: relative;
  background-color: lightgray;
  color: #000;
  border-radius: ${(props) => (props.isRounded ? "50%" : "8px")};
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: ${(props) => props.fontSize}px;
  text-transform: uppercase;
`;

const Avatar = ({ avatar, alt, size = 50, online = false, initals, isRounded = false }) => {
  const initialsFontSize = size >= 50 ? 18 : Math.floor(size * 0.36);

  return (
    <Container size={size ? size : 50} className="avatar">
      <Img isRounded={isRounded}>
        {avatar?.jpg ? (
          <img
            src={avatar?.jpg ? avatar?.jpg : empty_avatar}
            alt={alt}
            width={size ? size : 50}
            height={size ? size : 50}
            style={{ objectFit: "cover" }}
          />
        ) : (
          <Initals size={size ? size : 50} isRounded={isRounded} fontSize={initialsFontSize}>
            {initals || ""}
          </Initals>
        )}
      </Img>
      {online ? <Badge color="green" className="indicator" /> : null}
    </Container>
  );
};

Avatar.propTypes = {
  avatar: PropTypes.object,
  alt: PropTypes.string.isRequired,
  online: PropTypes.bool,
  size: PropTypes.number,
};

export default Avatar;
