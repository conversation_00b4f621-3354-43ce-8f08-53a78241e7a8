// components
import WidgetsLoader from "@components/WidgetsLoader";
import Page from "@layout/Page";
import NursesList from "@widgets/NursesList";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";

const Nurses = () => {
  const navigate = useNavigate();
  const { isLoading } = useSelector((state) => state.users);

  return (
    <Page title="Nurses List" btnText={"Add Nurse"} showRightBtn onClickBtn={() => navigate("/add_nurse")}>
      {isLoading ? <WidgetsLoader /> : null}
      {!isLoading ? <NursesList variant="nurse" /> : null}
    </Page>
  );
};

export default Nurses;
