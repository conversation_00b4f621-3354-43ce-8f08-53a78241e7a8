// styled components
import { Head<PERSON> } from "@components/Widget/style";
import { LetterN<PERSON>, LetterNavWrapper, LetterNavItem, NavWrapper } from "./style";

// components
import Widget from "@components/Widget";
import WidgetBody from "@components/Widget/WidgetBody";
import GenderNav from "@components/GenderNav";
import MonthNavigator from "@ui/Navigator/MonthNavigator";
import Group from "./Group";
import NoDataPlaceholder from "@components/NoDataPlaceholder";
import SearchBar from "@ui/SearchBar";

// utils
import { generateAlphabet } from "@utils/helpers";
import { nanoid } from "nanoid";

// hooks
import { useState, useRef, useEffect } from "react";
import useGenderFilter from "@hooks/useGenderFilter";

// data placeholder
import { useSelector } from "react-redux";

const PatientsList = () => {
  const { clients } = useSelector((state) => state.users);
  const contentRef = useRef(null);
  const [search, setSearch] = useState("");

  // current filter by month
  const [month, setMonth] = useState({ label: "This month", number: new Date().getMonth() });
  const filteredArr = clients.filter((item) => {
    // const monthMatch = item?.reg?.getMonth() === month.number;
    // if (!search) return monthMatch;

    const searchMatch = (item.firstName + " " + item.lastName).toLowerCase().includes(search.toLowerCase());

    return searchMatch;
  });

  // current filter by gender
  const { gender, setGender, genderArr } = useGenderFilter(filteredArr);

  // generate an array containing alphabet
  const alphabet = generateAlphabet();

  const isCharInPatients = (char, arr) => {
    let contains = false;
    for (let i = 0; i < arr.length; i++) {
      const firstLetter = arr[i].firstName?.charAt(0)?.toLowerCase();
      if (firstLetter === char) {
        contains = true;
        break;
      }
    }
    return contains;
  };

  useEffect(() => {
    contentRef.current?.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }, [month, gender]);

  // Get the actual array that will be rendered (considering gender filter)
  const currentArr = gender.value === "all" ? filteredArr : genderArr(gender);

  return (
    <Widget name="PatientsList">
      <Header sidePadding={true}>
        <NavWrapper>
          <GenderNav state={gender} handler={setGender} />
          {/* <MonthNavigator state={month} handler={setMonth} /> */}
        </NavWrapper>
        <LetterNavWrapper>
          <LetterNav>
            {alphabet.map((char) => {
              return (
                <li key={nanoid(3)}>
                  <LetterNavItem
                    className={
                      isCharInPatients(char, currentArr) ? "active" : ""
                    }
                    href={`#${char}`}
                  >
                    {char}
                  </LetterNavItem>
                </li>
              );
            })}
          </LetterNav>
        </LetterNavWrapper>
        <SearchBar placeholder="Search patient by name" handler={setSearch} value={search} />
      </Header>
      <WidgetBody style={{ padding: 0 }} elRef={contentRef}>
        {currentArr.length !== 0 ? (
          <>
            {alphabet.map((char) => {
              return (
                <Group
                  key={`patients-${char}`}
                  gender={gender.value}
                  char={char}
                  type={"patient"}
                  arr={currentArr}
                />
              );
            })}
          </>
        ) : (
          <NoDataPlaceholder />
        )}
      </WidgetBody>
    </Widget>
  );
};

export default PatientsList;
