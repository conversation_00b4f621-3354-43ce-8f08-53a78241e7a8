// components
import Navigator from '@ui/Navigator';

// utils
import moment from 'moment/moment';

const MonthlyNavigation = ({date, setter}) => {
    const calendarMonth = moment(date).month();
    const currentMonth = moment().month();
    const currentYear = moment().year();
    const calendarYear = moment(date).year();

    const handleMonthNavigation = e => {
        if (e.currentTarget.dataset.direction === 'prev') {
            setter(moment(date).subtract(1, 'month').toDate());
        } else {
            setter(moment(date).add(1, 'month').toDate());
        }
    }

    let label;
    if (currentMonth === calendarMonth && currentYear === calendarYear) label = 'This month';
    else if (currentMonth + 1 === calendarMonth && currentYear === calendarYear) label = 'Next month';
    else if (currentMonth - 1 === calendarMonth && currentYear === calendarYear) label = 'Last month';
    else label = `${moment(date).format('MMMM')}`;

    // Keep original navigation limits (no restriction for viewing)
    const isPrevDisabled = calendarMonth === 0;
    const isNextDisabled = calendarMonth === 11;

    return (
        <Navigator handler={handleMonthNavigation}
                   text={label}
                   prevDisabled={isPrevDisabled}
                   nextDisabled={isNextDisabled}/>
    )
}

export default MonthlyNavigation;