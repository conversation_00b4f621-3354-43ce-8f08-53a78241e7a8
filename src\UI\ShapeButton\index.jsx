// components
import { Badge } from "@ui/Badge/style";

// styling
import styled from "styled-components/macro";
import theme from "styled-theming";
import { dark, light, flex, colors } from "@styles/vars";

// utils
import PropTypes from "prop-types";
import { Menu, UserWrapper } from "@layout/Panel/style";
import { useState } from "react";
import ConfirmationModal from "@components/ConfirmationModal";
import { ClickAwayListener } from "@mui/base";

const bg = theme("theme", {
  light: light.bodyBg,
  dark: dark.highlight,
});

export const Button = styled.button`
  width: 40px;
  aspect-ratio: 1;
  // background-color: ${bg};
  color: ${colors.gray};
  ${flex.col}
  ${flex.center}
  position: relative;
  transition: color var(--transition), background-color var(--transition);

  &:hover,
  &:focus {
    background-color: ${colors.blue};
    color: #fff;
  }

  .badge {
    position: absolute;
  }

  &.square {
    border-radius: 8px;
    .badge {
      top: -6px;
      right: -6px;
    }
  }

  &.round {
    border-radius: 50%;
    .badge {
      top: 0;
      right: 0;
    }
  } 
    }
`;

const ShapeButton = ({ hasNotification, icon, handler, label, shape, ...props }) => {
  const [open, setOpen] = useState(false);
  const handleClickAway = () => setOpen(false);
  const handleClick = () => setOpen(!open);

  const [isLogoutOpen, setLogoutOpen] = useState(false);

  function onClickLogout() {
    handleClickAway();
    setLogoutOpen(true);
  }

  return (
    <>
      <ClickAwayListener onClickAway={handleClickAway}>
        <UserWrapper>
          <Button
            className={shape}
            onClick={() => {
              handleClick();
              if (handler) {
                handler();
              }
            }}
            aria-label={label}
            ref={props.ref}
            {...props}
          >
            <i className={`icon-${icon}`}></i>
            {/* {hasNotification && <Badge className="badge" color="yellow" />} */}
          </Button>

          <Menu className={open ? "visible" : ""}>
            <button>
              <i className="icon icon-circle-user" /> Change user
            </button>
            <button onClick={onClickLogout}>
              <i className="icon icon-logout" /> Logout
            </button>
          </Menu>
        </UserWrapper>
      </ClickAwayListener>

      <ConfirmationModal
        isOpen={isLogoutOpen}
        title="Logout?"
        subtitle="Are you sure you want to logout?"
        onConfirm={() => {
          /* handle logout */
        }}
        onCancel={() => setLogoutOpen(false)}
        confirmText="Logout"
        cancelText="Cancel"
      />
    </>
  );
};

ShapeButton.propTypes = {
  hasNotification: PropTypes.bool,
  icon: PropTypes.string.isRequired,
  handler: PropTypes.func,
  label: PropTypes.string.isRequired,
  shape: PropTypes.oneOf(["round", "square"]).isRequired,
};

export default ShapeButton;
